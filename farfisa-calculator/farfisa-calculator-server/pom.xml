<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2022 Adoreboard Ltd. All rights reserved.
  ~ ADOREBOARD PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>farfisa-calculator</artifactId>
        <groupId>com.adoreboard</groupId>
        <version>19.4.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>farfisa-calculator-server</artifactId>
    <packaging>war</packaging>

    <dependencies>
        <dependency>
            <groupId>com.adoreboard</groupId>
            <artifactId>farfisa-calculator-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- Spring dependencies -->

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-web</artifactId>
        </dependency>

        <!-- Provided -->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
        </dependency>
    </dependencies>
</project>