/*
 * Copyright 2022 Adoreboard Ltd. All rights reserved.
 * ADOREBOARD PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */

package com.adoreboard.emotics.common.mapper;

import com.adoreboard.emotics.common.enums.CustomerBusinessSector;
import com.adoreboard.emotics.common.enums.ProductFeature;
import com.adoreboard.emotics.common.model.User;
import com.adoreboard.emotics.common.model.UserTier;
import com.adoreboard.emotics.common.model.organisation.Organisation;
import org.joda.time.DateTime;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.*;

/**
 * Unit test to test the mappings of the MyBatis mappings to the UserMapper interface.
 * <p>
 * This is an integration test as it requires a live database to run on.
 * <p>
 * NOTE: if this unit test fails it may indicate that the test database hasn't been created i.e. "farfisa_test". Run the maven "mvn install" to set up the test database.
 *
 * <AUTHOR> Marks
 * <AUTHOR> Huy Vu
 */
public class UserMapperMyBatisTest extends AbstractMapperTest {

    // Constants

    private static final int TEST_USER_ID = 98;

    // Fields

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private RegistrationMapper registrationMapper;

    @Autowired
    private WorkspaceMapper workspaceMapper;

    @Autowired
    private OrganisationMapper organisationMapper;

    @Override
    @Before
    public void setup() throws Exception {
        insertPostgreSqlTestData();
    }

    @Override
    @After
    public void tearDown() throws Exception {
        deletePostgreSqlTestData();
    }

    // ===================================================================
    // CRUD methods
    // ===================================================================

    @Test
    public void shouldSelectUserByUsername() {

        // Given

        // When

        User user = userMapper.selectUserByUsername("testuser");
        User invalidUser = userMapper.selectUserByUsername("invalid");

        // Then

        assertEqualsUser98(user);
        assertNull("User should be null", invalidUser);
    }

    @Test
    public void shouldSelectUserByUsernameNotCaseSensitive() {

        // Given

        // When

        User user = userMapper.selectUserByUsername("TestUser");

        // Then

        assertEqualsUser98(user);
    }

    @Test
    public void shouldSelectUserByUsernameNotCaseSensitiveWithTrailingSpaces() {

        // Given

        // When

        User user = userMapper.selectUserByUsername("  TestUser    ");

        // Then

        assertEqualsUser98(user);
    }


    @Test
    public void shouldSelectUserById() {

        // Given

        // When

        User user = userMapper.selectUserById(98);
        User invalidUser = userMapper.selectUserById(13);

        // Then

        assertEqualsUser98(user);
        assertNull("User should be null", invalidUser);
    }

    @Test
    public void shouldInsertUser() {

        // Setup

        DateTime createdOn = new DateTime("2001-01-12T13:14:15.000Z");
        DateTime updatedOn = new DateTime("2002-12-21T13:24:35.000Z");

        // Given
        UserTier userTier = new UserTier(1);

        User user = new User();
        user.setLoginName("vu_login");
        user.setCreatedOn(createdOn);
        user.setUpdatedOn(updatedOn);
        user.setSecret("encoded password");
        user.setFirstName("Vu");
        user.setLastName("Le");
        user.setEmail("<EMAIL>");
        user.setRole(4);
        user.setLoginCount(25);
        user.setActive(false);
        user.setUserTier(userTier);

        // When
        userMapper.insertUser(user);

        // Then
        Map<String, Object> createdUser = jdbcTemplate.queryForMap("select * from users where login_name = 'vu_login'");
        assertNotNull(createdUser.get("id"));
        assertEquals(1, createdUser.get("tier_id"));
        assertEquals("vu_login", createdUser.get("login_name"));
        assertEquals(createdOn.getMillis(), ((Date) createdUser.get("created_on")).getTime());
        assertEquals(updatedOn.getMillis(), ((Date) createdUser.get("updated_on")).getTime());
        assertEquals("encoded password", createdUser.get("secret"));
        assertEquals("Vu", createdUser.get("first_name"));
        assertEquals("Le", createdUser.get("last_name"));
        assertEquals("<EMAIL>", createdUser.get("email"));
        assertEquals(4, createdUser.get("role"));
        assertEquals(25, createdUser.get("login_count"));
        assertEquals(false, createdUser.get("active"));
        assertNotNull(createdUser.get("created_on"));
    }

    @Test
    public void shouldUpdateUser() throws Exception {

        // Setup
        DateTime createdOn = new DateTime("1999-07-16T23:52:41.000Z");
        DateTime updatedOn = new DateTime("1998-06-15T22:51:40.000Z");
        executeSql("update users set product_features = null where id = " + TEST_USER_ID);

        // Given
        UserTier userTier = new UserTier(4);

        User user = new User();
        user.setId(TEST_USER_ID);
        user.setLoginName("new login name");
        user.setCreatedOn(createdOn);
        user.setUpdatedOn(updatedOn);
        user.setSecret("new secret");
        user.setFirstName("New Firstname");
        user.setLastName("New Lastname");
        user.setEmail("newmeail.com");
        user.setRole(5);
        user.setLoginCount(35);
        user.setActive(true);
        user.setUserTier(userTier);
        user.setSector(CustomerBusinessSector.BEAUTY);
        user.setContactNumber("0123456");

        // When

        int updatedRows = userMapper.updateUser(user);

        // Then

        assertEquals(1, updatedRows);
        Map<String, Object> results = jdbcTemplate.queryForMap("SELECT * FROM users WHERE login_name = 'new login name'");
        assertEquals(98, results.get("id"));
        assertEquals("new login name", results.get("login_name"));
        assertEquals("new secret", results.get("secret"));
        assertEquals("New Firstname", results.get("first_name"));
        assertEquals("New Lastname", results.get("last_name"));
        assertEquals("newmeail.com", results.get("email"));
        assertEquals(5, results.get("role"));
        assertEquals(35, results.get("login_count"));
        assertEquals("BEAUTY", results.get("sector"));
        assertTrue((boolean) results.get("active"));
    }

    @Test
    public void shouldDeleteUsers() throws Exception {
        // Setup (delete from so we don't get a DataIntegrityViolationException
        clearTables("storyteller_slide", "storyteller_report", "user_topic_doc", "user_topic", "user_doc", "user_content", "stop_word", "dataset_download_details", "dataset_custom_charts", "dataset_permission", "bulk_upload", "registration", "usage_record", "reporting_job");

        // When / Then
        assertEquals(5, numberOfRowsIn("users"));

        userMapper.deleteUser(98);
        assertEquals(4, numberOfRowsIn("users"));

        // We need to remove this workspace because the proceeding user (99) is the workspace owner in the tests.
        workspaceMapper.removeWorkspace(99);
        workspaceMapper.removeWorkspace(98);
        organisationMapper.removeOrganisation(99);

        userMapper.deleteUser(99);
        assertEquals(3, numberOfRowsIn("users"));

        userMapper.deleteUser(-999);
        assertEquals(3, numberOfRowsIn("users"));

        userMapper.deleteUser(101);
        assertEquals(2, numberOfRowsIn("users"));
    }

    @Test
    public void shouldDeleteUsersCascade() {

        // Given
        registrationMapper.deleteRegistration("<EMAIL>"); // Do this so registration constraint doesn't trip

        assertThat (numberOfRowsIn("users", "id = 98")).isEqualTo(1);
        assertThat (numberOfRowsIn("bulk_upload", "user_id = 98")).isEqualTo(2);
        assertThat (numberOfRowsIn("user_doc", "user_id = 98")).isEqualTo(6);
        assertThat (numberOfRowsIn("stop_word", "bulk_upload_id = 1000")).isEqualTo(3);

        // When

        userMapper.deleteUserCascade(98, "testuser");

        // Then

        assertThat(numberOfRowsIn("users", "id = 98")).isZero();
        assertThat(numberOfRowsIn("bulk_upload", "user_id = 98")).isZero();
        assertThat(numberOfRowsIn("user_doc", "user_id = 98")).isZero();
        assertThat(numberOfRowsIn("stop_word", "bulk_upload_id = (select id from bulk_upload where user_id = 98)")).isZero();

    }

    @Test
    public void shouldUpdateSurveyMonkeyToken() {

        // Given

        String newToken = "sm_token";

        // When

        userMapper.updateSurveyMonkeyToken(98, newToken);

        // Then

        String smToken = jdbcTemplate.queryForObject("SELECT survey_monkey_token FROM users WHERE id = 98", String.class);

        assertEquals(newToken, smToken);
    }

    @Test
    public void shouldCheckAuthenticationSurveyMonkey() {

        // Given

        jdbcTemplate.execute("UPDATE users SET survey_monkey_token = '123abc' WHERE id = 98");

        // When

        Boolean authenticated = userMapper.isSurveyMonkeyAuthenticated(98);

        // Then

        assertEquals(true, authenticated);
    }

    @Test
    public void shouldUpdateZendeskToken() {

        // Given

        String newToken = "zd_token";

        // When

        userMapper.updateZendeskToken(98, newToken);

        // Then

        String zdToken = jdbcTemplate.queryForObject("SELECT zendesk_token FROM users WHERE id = 98", String.class);

        assertEquals(newToken, zdToken);
    }

    @Test
    public void shouldEnable2Fa() {

        // Given

        // When

        userMapper.update2faAuthentication(98, true, "some secret");

        // Then

        Map<String, Object> rs = jdbcTemplate.queryForMap("SELECT two_factor_enabled, secret_2fa_code FROM users WHERE id = 98");

        assertEquals("some secret", rs.get("secret_2fa_code"));
        assertTrue("Should be true", (Boolean) rs.get("two_factor_enabled"));
    }

    @Test
    public void shouldDisableFa() {

        // Given

        // When

        userMapper.update2faAuthentication(98, false, null);

        // Then

        Map<String, Object> rs = jdbcTemplate.queryForMap("SELECT two_factor_enabled, secret_2fa_code FROM users WHERE id = 98");

        assertNull("Should be empty", rs.get("secret_2fa_code"));
        assertFalse("Should be false", (Boolean) rs.get("two_factor_enabled"));
    }

    @Test
    public void shouldCheckAuthenticationZendesk() {

        // Given

        jdbcTemplate.execute("UPDATE users SET zendesk_token = '123abc' WHERE id = 98");

        // When

        boolean authenticated = userMapper.isZendeskAuthenticated(98);

        // Then

        assertEquals(true, authenticated);
    }

    @Test
    public void shouldGetAllUsersWithInternalTier() {
        List<User> users = userMapper.selectAllUsers("id", "asc", 10, 0);
        for (User u : users) {
            assertEquals(u.getUserTier().getProductFeatures(), u.getProductFeatures());
        }
        assertEquals(5, users.size());
    }

    // private methods

    /**
     *
     * @param testedUser
     */
    private void assertEqualsUser98(User testedUser) {
        assertEquals(TEST_USER_ID, testedUser.getId());
        assertEquals("testuser", testedUser.getLoginName());
        assertEquals("$2a$04$Z8ij", testedUser.getPassword());
        assertEquals("Test", testedUser.getFirstName());
        assertEquals("Person", testedUser.getLastName());
        assertEquals("<EMAIL>", testedUser.getEmail());
        assertEquals(1, testedUser.getRole());
        assertEquals(10, testedUser.getLoginCount());
        assertTrue("Active should be true", testedUser.isActive());
        assertEquals(1, testedUser.getUserTier().getId());
        assertEquals("tier1", testedUser.getUserTier().getName());
        assertEquals(123, testedUser.getUserTier().getCost());
        assertEquals(10, testedUser.getUserTier().getMaxChars());
        assertEquals(5, testedUser.getUserTier().getMaxUploads());
        assertEquals(17, testedUser.getUserTier().getDuration());
        assertEquals("stripe_plan_id_1", testedUser.getUserTier().getStripePlanId());
        assertEquals("SM_Token", testedUser.getSurveyMonkeyToken());
        assertEquals(99, testedUser.getOrganisationId().intValue());
        assertThat(testedUser.getProductFeatures()).contains(ProductFeature.swot, ProductFeature.multilingual);

        assertThat(testedUser.getUserTier().getProductFeatures()).contains(ProductFeature.swot, ProductFeature.multilingual);
    }

    @Test
    public void shouldInsertWorkspaceAndOrganisationToUser() {
        User user = userMapper.selectUserByUsername("TestUser");

        user.getWorkspaceIds().add(1);

        userMapper.updateWorkspaceIds(user.getId(), user.getWorkspaceIds());
        userMapper.updateOrganisationId(user.getId(), 1);
        user = userMapper.selectUserByUsername("TestUser");

        assertTrue(user.getWorkspaceIds().contains(1));
        assertEquals(1, (int) user.getOrganisationId());
    }


    @Test
    public void shouldRemoveWorkspaceToUser() {
        User user = userMapper.selectUserByUsername("TestUser");

        user.getWorkspaceIds().add(1);
        user.getWorkspaceIds().add(2);
        userMapper.updateWorkspaceIds(user.getId(), user.getWorkspaceIds());
        user = userMapper.selectUserByUsername("TestUser");
        assertThat(user.getWorkspaceIds()).hasSize(2);

        user.getWorkspaceIds().remove(1);
        assertThat(user.getWorkspaceIds()).hasSize(1);
    }

}