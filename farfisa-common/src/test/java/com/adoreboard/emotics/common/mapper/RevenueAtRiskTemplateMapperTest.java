package com.adoreboard.emotics.common.mapper;

import com.adoreboard.emotics.common.enums.RevenueAtRiskType;
import com.adoreboard.emotics.common.enums.RevenueAtRiskWeight;
import com.adoreboard.emotics.common.model.enums.RarCurrency;
import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplate;
import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplateInfo;
import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplateWithUser;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.Assert.*;

public class RevenueAtRiskTemplateMapperTest extends AbstractMapperTest {
    @Autowired
    private RevenueAtRiskTemplateMapper revenueAtRiskTemplateMapper;

    @Before
    public void setup() throws Exception {
        insertPostgreSqlTestData();
    }

    @After
    public void tearDown() throws Exception {
        deletePostgreSqlTestData();
    }

    @Test
    public void shouldCreateWorkspaceTemplate() {
        // Given
        RevenueAtRiskTemplateInfo varInfo = createTestValueAtRiskInfo();
        RevenueAtRiskTemplate template = RevenueAtRiskTemplate.builder()
                .name("Test Workspace Template")
                .workspaceId(99)
                .createdBy(98)
                .revenueAtRisk(varInfo)
                .defaultTemplate(false)
                .createdAt(LocalDateTime.now())
                .build();

        // When
        revenueAtRiskTemplateMapper.createWorkspaceTemplate(template);

        // Then
        assertNotNull(template.getId());

        RevenueAtRiskTemplate created = revenueAtRiskTemplateMapper.getTemplateById(template.getId());
        assertNotNull(created);
        assertEquals("Test Workspace Template", created.getName());
        assertEquals(Integer.valueOf(99), created.getWorkspaceId());
        assertEquals(98, created.getCreatedBy());
        assertFalse(created.isDefaultTemplate());
        assertNotNull(created.getRevenueAtRisk());
        assertEquals(RevenueAtRiskType.CUSTOMER, created.getRevenueAtRisk().getRevenueAtRiskType());
    }

    @Test
    public void shouldGetWorkspaceTemplates() {
        // Given
        createTestTemplate("Template 1", 99, false);
        createTestTemplate("Template 2", 99, true);
        createTestTemplate("Template 3", 98, false); // Different workspace

        // When
        List<RevenueAtRiskTemplateWithUser> templates = revenueAtRiskTemplateMapper.getWorkspaceTemplates(99);

        // Then
        assertEquals(2, templates.size());
        assertTrue(templates.stream().allMatch(x -> x.getName().equals("Template 1") || x.getName().equals("Template 2")));
        assertFalse(templates.stream().allMatch(x -> x.getName().equals("Template 3")));
    }

    @Test
    public void shouldUpdateTemplateRarInfo() {
        // Given
        RevenueAtRiskTemplate template = createTestTemplate("Original Name", 99, false);

        // When
        template.setName("Updated Name");
        RevenueAtRiskTemplateInfo updatedVarInfo = createTestValueAtRiskInfo();
        updatedVarInfo.setNumberOfCustomers(500);
        template.setRevenueAtRisk(updatedVarInfo);

        revenueAtRiskTemplateMapper.updateTemplateRarInfo(template);

        // Then
        RevenueAtRiskTemplate updated = revenueAtRiskTemplateMapper.getTemplateById(template.getId());
        assertEquals("Updated Name", updated.getName());
        assertEquals(Integer.valueOf(500), updated.getRevenueAtRisk().getNumberOfCustomers());
    }

    @Test
    public void shouldSoftDeleteTemplate() {
        // Given
        RevenueAtRiskTemplate template = createTestTemplate("To Delete", 99, false);
        Integer templateId = template.getId();

        // When
        revenueAtRiskTemplateMapper.softDeleteTemplate(templateId);

        // Then
        RevenueAtRiskTemplate deleted = revenueAtRiskTemplateMapper.getTemplateById(templateId);
        assertNull(deleted); // Should not be found due to status = 'deleted'
    }

    @Test
    public void shouldGetDefaultTemplate() {
        // Given
        createTestTemplate("Non-Default", 99, false);
        RevenueAtRiskTemplate defaultTemplate = createTestTemplate("Default Template", 99, true);

        // When
        RevenueAtRiskTemplate found = revenueAtRiskTemplateMapper.getDefaultTemplate(99);

        // Then
        assertNotNull(found);
        assertEquals(defaultTemplate.getId(), found.getId());
        assertEquals("Default Template", found.getName());
        assertTrue(found.isDefaultTemplate());
    }

    @Test
    public void shouldUnsetDefaultTemplate() {
        // Given
        RevenueAtRiskTemplate defaultTemplate = createTestTemplate("Default", 99, true);

        // When
        revenueAtRiskTemplateMapper.unsetDefaultTemplate(99);

        // Then
        RevenueAtRiskTemplate updated = revenueAtRiskTemplateMapper.getTemplateById(defaultTemplate.getId());
        assertFalse(updated.isDefaultTemplate());

        RevenueAtRiskTemplate noDefault = revenueAtRiskTemplateMapper.getDefaultTemplate(99);
        assertNull(noDefault);
    }

    @Test
    public void shouldSetDefaultTemplate() {
        // Given
        RevenueAtRiskTemplate template = createTestTemplate("To Be Default", 99, false);

        // When
        revenueAtRiskTemplateMapper.setDefaultTemplate(template.getId());

        // Then
        RevenueAtRiskTemplate updated = revenueAtRiskTemplateMapper.getTemplateById(template.getId());
        assertTrue(updated.isDefaultTemplate());
    }

    @Test
    public void shouldCheckTemplateNameExists() {
        // Given
        createTestTemplate("Existing Template", 99, false);

        // When & Then
        assertTrue(revenueAtRiskTemplateMapper.existsTemplateByName(99, "Existing Template"));
        assertFalse(revenueAtRiskTemplateMapper.existsTemplateByName(99, "Non-Existing Template"));
        assertFalse(revenueAtRiskTemplateMapper.existsTemplateByName(100, "Existing Template")); // Different workspace
    }

    @Test
    public void shouldHandleMultipleDefaultTemplatesScenario() {
        // Given - Manually create scenario where multiple templates might be default
        RevenueAtRiskTemplate template1 = createTestTemplate("Template 1", 99, true);
        RevenueAtRiskTemplate template2 = createTestTemplate("Template 2", 99, false);

        // When - Set second template as default (should unset first)
        revenueAtRiskTemplateMapper.unsetDefaultTemplate(99);
        revenueAtRiskTemplateMapper.setDefaultTemplate(template2.getId());

        // Then
        RevenueAtRiskTemplate updated1 = revenueAtRiskTemplateMapper.getTemplateById(template1.getId());
        RevenueAtRiskTemplate updated2 = revenueAtRiskTemplateMapper.getTemplateById(template2.getId());

        assertFalse(updated1.isDefaultTemplate());
        assertTrue(updated2.isDefaultTemplate());

        RevenueAtRiskTemplate defaultTemplate = revenueAtRiskTemplateMapper.getDefaultTemplate(99);
        assertEquals(template2.getId(), defaultTemplate.getId());
    }

    @Test
    public void shouldGetTemplateByIdReturnNullForDeletedTemplate() {
        // Given
        RevenueAtRiskTemplate template = createTestTemplate("To Delete", 99, false);

        // When
        revenueAtRiskTemplateMapper.softDeleteTemplate(template.getId());

        // Then
        RevenueAtRiskTemplate result = revenueAtRiskTemplateMapper.getTemplateById(template.getId());
        assertNull(result); // Should not return deleted templates
    }

    @Test
    public void shouldHandleComplexValueAtRiskInfo() {
        // Given - Complex ValueAtRiskInfo with all fields populated
        RevenueAtRiskTemplateInfo complexVarInfo = new RevenueAtRiskTemplateInfo();
        complexVarInfo.setRevenueAtRiskType(RevenueAtRiskType.CUSTOMER);
        complexVarInfo.setRevenueAtRiskWeight(RevenueAtRiskWeight.HIGH);
        complexVarInfo.setCurrency(RarCurrency.EUR);
        complexVarInfo.setCustomerSpendAvgAnnual(1000.0);
        complexVarInfo.setCustomerAdditionalCost(200.0);
        complexVarInfo.setNumberOfCustomers(100);
        complexVarInfo.setTotalYear(2);
        complexVarInfo.setEmployeeSalary(50000.0);
        complexVarInfo.setEmployeeAdditionalCost(10000.0);
        complexVarInfo.setNumberOfEmployees(50);
        complexVarInfo.setScaleToTotalPeople(true);

        RevenueAtRiskTemplate template = RevenueAtRiskTemplate.builder()
                .name("Complex Template")
                .workspaceId(99)
                .createdBy(98)
                .revenueAtRisk(complexVarInfo)
                .defaultTemplate(false)
                .createdAt(LocalDateTime.now())
                .build();

        // When
       revenueAtRiskTemplateMapper.createWorkspaceTemplate(template);

        // Then
        RevenueAtRiskTemplate retrieved = revenueAtRiskTemplateMapper.getTemplateById(template.getId());
        RevenueAtRiskTemplateInfo retrievedVarInfo = retrieved.getRevenueAtRisk();

        assertEquals(complexVarInfo.getRevenueAtRiskType(), retrievedVarInfo.getRevenueAtRiskType());
        assertEquals(complexVarInfo.getRevenueAtRiskWeight(), retrievedVarInfo.getRevenueAtRiskWeight());
        assertEquals(complexVarInfo.getCurrency(), retrievedVarInfo.getCurrency());
        assertEquals(complexVarInfo.getCustomerSpendAvgAnnual(), retrievedVarInfo.getCustomerSpendAvgAnnual());
        assertEquals(complexVarInfo.getCustomerAdditionalCost(), retrievedVarInfo.getCustomerAdditionalCost());
        assertEquals(complexVarInfo.getNumberOfCustomers(), retrievedVarInfo.getNumberOfCustomers());
        assertEquals(complexVarInfo.getTotalYear(), retrievedVarInfo.getTotalYear());
        assertEquals(complexVarInfo.getEmployeeSalary(), retrievedVarInfo.getEmployeeSalary());
        assertEquals(complexVarInfo.getEmployeeAdditionalCost(), retrievedVarInfo.getEmployeeAdditionalCost());
        assertEquals(complexVarInfo.getNumberOfEmployees(), retrievedVarInfo.getNumberOfEmployees());
        assertEquals(complexVarInfo.getScaleToTotalPeople(), retrievedVarInfo.getScaleToTotalPeople());
    }

    // Helper methods
    private RevenueAtRiskTemplate createTestTemplate(String name, Integer workspaceId, boolean isDefault) {
        RevenueAtRiskTemplateInfo varInfo = createTestValueAtRiskInfo();
        RevenueAtRiskTemplate template = RevenueAtRiskTemplate.builder()
                .name(name)
                .workspaceId(workspaceId)
                .createdBy(98)
                .revenueAtRisk(varInfo)
                .defaultTemplate(isDefault)
                .createdAt(LocalDateTime.now())
                .build();

        revenueAtRiskTemplateMapper.createWorkspaceTemplate(template);
        return template;
    }

    private RevenueAtRiskTemplateInfo createTestValueAtRiskInfo() {
        RevenueAtRiskTemplateInfo varInfo = new RevenueAtRiskTemplateInfo();
        varInfo.setRevenueAtRiskType(RevenueAtRiskType.CUSTOMER);
        varInfo.setRevenueAtRiskWeight(RevenueAtRiskWeight.MEDIUM);
        varInfo.setCurrency(RarCurrency.USD);
        varInfo.setCustomerSpendAvgAnnual(500.0);
        varInfo.setCustomerAdditionalCost(100.0);
        varInfo.setNumberOfCustomers(10);
        varInfo.setTotalYear(1);
        varInfo.setScaleToTotalPeople(false);
        return varInfo;
    }
}