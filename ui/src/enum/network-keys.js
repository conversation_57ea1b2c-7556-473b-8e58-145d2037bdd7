import { Enum } from 'enumify';

/**
 * @enum
 */
class NetworkKeys extends Enum {}

NetworkKeys.initEnum(
  /** @lends NetworkKeys */ [
    'BENCHMARK_GET',
    'CUSTOM_TOPICS_CREATE',
    'CUSTOM_TOPICS_REMOVE',
    'CUSTOM_TOPICS_UPDATE',
    'CUSTOM_TOPICS_CREATE_LIST',
    'CUSTOM_TOPICS_CREATE_LIST_FILE',
    'CUSTOM_TOPICS_GET_LIST',
    'CUSTOM_TOPICS_GET_LISTS',
    'CUSTOM_TOPICS_GET_TOPICS',
    'CUSTOM_TOPICS_REMOVE_LIST',
    'CUSTOM_TOPICS_UPDATE_LIST',
    'DATA_IMPORT_CREATE',
    'DATA_IMPORT_PREVIEW',
    'DATASETS',
    'DATASETS_ARCHIVE',
    'DATASETS_CANCEL',
    'DATASETS_COPY_TO_ANOTHER_WORKSPACE',
    'DATASETS_CUSTOM_CHART',
    'DATASETS_DESTROY',
    'DATASETS_EXPORT',
    'DATASETS_MERGE',
    'DATASETS_METADATA',
    'DATASETS_OVERVIEWS',
    'DATASETS_PERMISSION',
    'DATASETS_PERMISSION_CREATE',
    'DATASETS_PERMISSION_DELETE',
    'DATASETS_PERMISSION_UPDATE',
    'DATASETS_POLL',
    'DATASETS_PROGRESS',
    'DATASETS_REANALYSE',
    'DATASETS_REFRESH',
    'DATASETS_REMOVE',
    'DATASETS_RENAME',
    'DATASETS_SAMPLE',
    'DATASETS_SELECTED',
    'DATASETS_SUMMARIES',
    'DATASETS_SUMMARY',
    'DATASETS_UPDATE_METADATA_HEADER',
    'GENESIS_CORRELATION',
    'GENESIS_IMPROVEMENTS',
    'GENESIS_METADATA_CHANGES',
    'GLOBAL_STOPWORDS_CREATE',
    'GLOBAL_STOPWORDS_EDIT',
    'GLOBAL_STOPWORDS_GET',
    'GLOBAL_STOPWORDS_REMOVE',
    'INSIGHTS_ACTIONS_PLAN',
    'INSIGHTS_ACTIONS_LIST',
    'INSIGHTS_ACTIONS_SUMMARY',
    'INSIGHTS_COMMENTS',
    'INSIGHTS_COMMENTS_UPDATE',
    'INSIGHTS_CORRELATIONS_UPDATE',
    'INSIGHTS_DETAILS',
    'INSIGHTS_DETAILS_HEADLINE',
    'INSIGHTS_DETAILS_KEY_AREA',
    'INSIGHTS_DETAILS_KEY_AREA_TITLE',
    'INSIGHTS_DETAILS_TITLE',
    'INSIGHTS_EMOTIONS_UPDATE',
    'INSIGHTS_HEADLINE',
    'INSIGHTS_SCORECARD',
    'INSIGHTS_THEMES',
    'INSIGHTS_UPDATE_TOP_INDICATORS',
    'INVOICE',
    'JOURNEYS_CREATE',
    'JOURNEYS_DELETE',
    'JOURNEYS_GET',
    'JOURNEYS_UPDATE',
    'JOURNEYS_STAGES_CREATE',
    'JOURNEYS_STAGES_DELETE',
    'JOURNEYS_STAGES_GET',
    'JOURNEYS_STAGES_UPDATE',
    'MAINTENANCE',
    'METADATA_FILTER_COMMENT',
    'METADATA_FILTER_VIEW_DELETE',
    'METADATA_FILTER_VIEW_GET',
    'METADATA_FILTER_VIEW_PROMOTE',
    'METADATA_FILTER_VIEW_SAVE',
    'METADATA_NUMERIC_RANGE',
    'METADATA_TEXT_DISTINCT',
    'ORGANISATION',
    'PAYMENT_CARD',
    'PAYMENT_CARD_DELETE',
    'PAYMENT_CARD_GET',
    'PAYMENT_CONFIRM',
    'PAYMENT_DEFAULT_CARD',
    'QUALTRICS_AUTHENTICATION',
    'QUALTRICS_AUTHENTICATION_URL',
    'QUALTRICS_DISCONNECT',
    'QUALTRICS_SURVEYS_GET',
    'QUALTRICS_SURVEYS_IMPORT',
    'REGISTRATION',
    'REGISTRATION_VERIFY',
    'REVENUE_AT_RISK_CREATE',
    'REVENUE_AT_RISK_DELETE',
    'REVENUE_AT_RISK_FETCH',
    'REVENUE_AT_RISK_GET',
    'REVENUE_AT_RISK_UPDATE',
    'SAVED_ACTIONS',
    'SAVED_ACTIONS_CREATE',
    'SAVED_ACTIONS_DELETE',
    'SAVED_ACTIONS_UPDATE',
    'SAVED_ACTION_LISTS',
    'SAVED_ACTION_LISTS_APPLY',
    'SAVED_ACTION_LISTS_CREATE',
    'SAVED_ACTION_LISTS_DELETE',
    'SAVED_ACTION_LISTS_UPDATE',
    'SEARCH',
    'SEARCH_CREATE',
    'SEARCH_CREATE_TOPIC',
    'SEARCH_DELETE_RESULT',
    'SEARCH_MORE',
    'SEARCH_FILTERS',
    'SEARCH_FILTERS_EDIT',
    'SEARCH_FILTERS_DELETE',
    'SEARCH_FILTERS_SAVE',
    'SEARCH_QUERY',
    'SEARCH_QUERY_EDIT',
    'SEARCH_QUERY_DELETE',
    'SEARCH_QUERY_SAVE',
    'SEARCH_TOPIC',
    'SEARCH_VALIDATE',
    'SEARCH_VALIDATE_TOPIC',
    'SNIPPETS_BOOKMARKS_ADD',
    'SNIPPETS_BOOKMARKS',
    'SNIPPETS_BOOKMARKS_REMOVE',
    'SNIPPETS',
    'SNIPPETS_COUNT',
    'SNIPPETS_FLAG',
    'SNIPPETS_KEY',
    'SNIPPETS_REASSIGN',
    'SNIPPETS_REASSIGN_SEARCH',
    'SNIPPETS_REMOVE',
    'SNIPPETS_TOPICS',
    'SNIPPETS_TOPICS_FOR_SEARCH',
    'STOPWORDS',
    'STOPWORDS_SUBMIT',
    'STOPWORDS_WORKSPACE',
    'STOPWORDS_WORKSPACE_SUBMIT',
    'STORYTELLER_ACTION_PLANS_ADD_SUGGESTED_ACTION',
    'STORYTELLER_ACTION_PLANS_DELETE_SUGGESTED_ACTION',
    'STORYTELLER_ACTION_PLANS_GENERATE_AI_TEXT',
    'STORYTELLER_ACTION_PLANS_REORDER_THEMES',
    'STORYTELLER_ACTION_PLANS_REVERT_ACTION_IMPACT',
    'STORYTELLER_ACTION_PLANS_REVERT_SUGGESTED_ACTION',
    'STORYTELLER_ACTION_PLANS_SLIDE',
    'STORYTELLER_ACTION_PLANS_UPDATE_ACTION_IMPACT',
    'STORYTELLER_ACTION_PLANS_UPDATE_SETTINGS',
    'STORYTELLER_ACTION_PLANS_UPDATE_SUGGESTED_ACTION',
    'STORYTELLER_ACTION_PLANS_UPDATE_THEMES',
    'STORYTELLER_CHECK_STATUS',
    'STORYTELLER_DELETE_REPORT',
    'STORYTELLER_GET_REPORT',
    'STORYTELLER_GET_REPORTS',
    'STORYTELLER_REFRESH_REPORT',
    'STORYTELLER_REORDER_SELECTED_THEMES',
    'STORYTELLER_REPORT_GENERATE',
    'STORYTELLER_DOWNLOAD_REPORT',
    'STORYTELLER_SLIDE_GET',
    'STORYTELLER_SLIDES_GENERATE_AI_TEXT',
    'STORYTELLER_SLIDES_GENERATE_TITLE',
    'STORYTELLER_SLIDES_GET',
    'STORYTELLER_SLIDES_REVERT_TEXT',
    'STORYTELLER_SLIDES_UPDATE',
    'STORYTELLER_SLIDES_UPDATE_SELECTED_COMMENTS',
    'STORYTELLER_UPDATE_COMMENT_SETTINGS',
    'STORYTELLER_UPDATE_REPORT_SETTINGS',
    'STORYTELLER_UPDATE_SELECTED_THEMES',
    'STORYTELLER_UPDATE_SLIDE_DID_YOU_KNOW',
    'SURVEYMONKEY_AUTHENTICATE',
    'SURVEYMONKEY_DISCONNECT',
    'SURVEYMONKEY_QUESTIONS',
    'SURVEYMONKEY_SURVEYS',
    'SURVEYMONKEY_URL',
    'SURVEYMONKEY_IMPORT_QUESTIONS',
    'SURVEYMONKEY_POLL_AUTHENTICATIONS',
    'SURVEYS',
    'TAGS',
    'TAGS_ADD_TO_DATASET',
    'TAGS_CREATE',
    'TAGS_GET_FOR_DATASET',
    'TAGS_REMOVE',
    'TAGS_REMOVE_FROM_DATASET',
    'THEMES',
    'THEMES_BUILDER_CREATE',
    'THEMES_BUILDER_CREATE_NEW_DATASET',
    'THEMES_CREATE_DATASET_FROM_TOPICS',
    'THEMES_INTENSITY_COUNTS',
    'THEMES_KEY',
    'THEMES_KEY_TOPICS',
    'THEMES_MERGE',
    'THEMES_REMOVE',
    'THEMES_RENAME',
    'THEMES_SUMMARY',
    'THEMES_THEME_IDS',
    'THEMES_TOPIC',
    'THEMES_TOPIC_IDS',
    'THEMES_TOPIC_NAMES',
    'THEMES_TOPICS',
    'THEMES_UPGRADE',
    'TIME_SERIES',
    'TIME_SERIES_TOPICS',
    'TIME_SERIES_SNIPPETS',
    'UPLOADS',
    'UPLOADS_METADATA_ADD',
    'UPLOADS_METADATA_REMOVE',
    'UPLOADS_METADATA_UPDATE',
    'UPLOADS_PROCEED',
    'UPLOADS_REMOVE',
    'UPLOADS_UPDATE',
    'USAGE',
    'USER',
    'USER_CHANGE_PASSWORD',
    'USER_DISABLE',
    'USER_LOGIN',
    'USER_LOGOUT',
    'USER_LOGOUT_ALL',
    'USER_PASSWORD_REQUEST',
    'USER_PASSWORD_RESET',
    'USER_SAVE_DETAIL',
    'USER_SEND_EMAIL',
    'USER_SET_2FA',
    'USER_UNSUBSCRIBE',
    'USER_VERIFY',
    'VAR',
    'VAR_APPLY_TEMPLATE',
    'VAR_PREVIEW',
    'VAR_UPDATE',
    'WORKSPACE',
    'WORKSPACE_DISINVITE_USER',
    'WORKSPACE_GROUP',
    'WORKSPACE_GROUP_CREATE',
    'WORKSPACE_GROUP_DELETE',
    'WORKSPACE_GROUP_UPDATE',
    'WORKSPACE_GROUP_UPDATE_MEMBERS',
    'WORKSPACE_INVITE_USER',
    'WORKSPACE_UPDATE',
    'WORKSPACE_UPDATE_USERS',
    'ZENDESK_AUTHENTICATION',
    'ZENDESK_AUTHENTICATION_URL',
    'ZENDESK_DISCONNECT',
    'ZENDESK_IMPORT_TICKET',
    'ZENDESK_JWT',
    'ZENDESK_POLL_AUTHENTICATION',
    'ZENDESK_PREVIEW_TICKET',
  ],
);

export default NetworkKeys;
