import StorytellerSlideType from '@/enum/storyteller-slide-type';
import types from './types';

const g = types.getters;

export default {
  [g.activeSlides]: (state, getters) => {
    const displaySettings = getters[g.displaySlides];

    return state.slides.filter(slide => {
      if (slide.slideType === StorytellerSlideType.INTRO.name && displaySettings.intro) {
        return true;
      }
      if (slide.slideType === StorytellerSlideType.THEMES_INSIGHTS.name && displaySettings.themesInsights) {
        return true;
      }
      if (slide.slideType === StorytellerSlideType.PRESENTATION_FLOW.name && displaySettings.presentationFlow) {
        return true;
      }
      if (slide.slideType === StorytellerSlideType.INSIGHT.name && displaySettings.insight) {
        return true;
      }
      if (slide.slideType === StorytellerSlideType.DID_YOU_KNOW.name && displaySettings.didYouKnow) {
        return true;
      }
      if (slide.slideType === StorytellerSlideType.IMPLICATION.name && displaySettings.implication) {
        return true;
      }
      if (slide.slideType === StorytellerSlideType.COMMENTS.name && displaySettings.comments) {
        return true;
      }
      if (slide.slideType === StorytellerSlideType.ACTION_PLANS.name) {
        return true;
      }
      return true;
    });
  },

  [g.availableDisplays]: state => {
    const availableItems = state.activeReport.settings.availableInsightSettings;

    return {
      score: availableItems.includes('score'),
      volume: availableItems.includes('volume'),
      predictImprovement: availableItems.includes('predict_improvement'),
      valueAtRisk: availableItems.includes('value_at_risk'),
      swot: availableItems.includes('swot'),
    };
  },

  [g.displaySlides]: state => {
    const {
      displayCoverSlide: intro,
      displayThemesInsightsSlide: themesInsights,
      displayPresentationFlowSlide,
      displayInsightSlide: insight,
      displayDidYouKnowSlide: didYouKnow,
      displayImplicationSlide: implication,
      displayCommentSlide: comments,
    } = state.activeReport.settings.displaySettings;

    const presentationFlow = displayPresentationFlowSlide && insight && didYouKnow && implication && comments;

    return {
      intro,
      themesInsights,
      presentationFlow,
      insight,
      didYouKnow,
      implication,
      comments,
    };
  },

  [g.loadingSlides]: state => {
    return state.slides.filter(slide => slide.slideType === StorytellerSlideType.LOADING.name);
  },

  [g.slideFlow]: state => {
    return state.slides.find(slide => slide.slideType === StorytellerSlideType.PRESENTATION_FLOW.name);
  },

  [g.slideIntro]: state => {
    return state.slides.find(slide => slide.slideType === StorytellerSlideType.INTRO.name);
  },

  [g.slideThemesInsights]: state => {
    return state.slides.find(slide => slide.slideType === StorytellerSlideType.THEMES_INSIGHTS.name);
  },

  [g.themeSlides]: (state, getters) => {
    const themeSlides = {};
    state.activeReport.themeIds.forEach(themeId => {
      themeSlides[themeId] = [];
    });

    getters[g.activeSlides].forEach(slide => {
      const { themeId } = slide.slideData;
      if (themeId !== null && themeId !== undefined && themeSlides[themeId]) {
        themeSlides[themeId].push(slide);
      }
    });

    return themeSlides;
  },
};
