import acknowledgements from '@/store/modules/acknowledgements';
import benchmark from '@/store/modules/benchmark';
import chartOptions from '@/store/modules/chart-options';
import datasetManagementTabs from '@/store/modules/dataset-management-tabs';
import datasets from '@/store/modules/datasets';
import datasetsDelegator from '@/store/modules/datasets-delegator';
import datasetsImport from '@/store/modules/datasets-import';
import datasetsInsights from '@/store/modules/datasets-insights';
import datasetsProgress from '@/store/modules/datasets-progress';
import datasetsTag from '@/store/modules/datasets-tag';
import emotionChart from '@/store/modules/emotion-chart';
import globalStopWords from '@/store/modules/global-stop-words';
import invoices from '@/store/modules/invoices';
import journey from '@/store/modules/journey';
import layout from '@/store/modules/layout';
import maintenance from '@/store/modules/maintenance';
import modal from '@/store/modules/modal';
import network from '@/store/modules/network';
import onboarding from '@/store/modules/onboarding';
import organisation from '@/store/modules/organisation';
import pagedModal from '@/store/modules/paged-modal';
import payment from '@/store/modules/payment';
import poll from '@/store/modules/poll';
import qualtrics from '@/store/modules/qualtrics';
import registration from '@/store/modules/registration';
import revenueAtRiskTemplate from '@/store/modules/revenue-at-risk-template';
import savedActions from '@/store/modules/saved-actions';
import savedActionsBuilder from '@/store/modules/saved-actions-builder';
import search from '@/store/modules/search';
import searchFilters from '@/store/modules/search-filters';
import searchQueries from '@/store/modules/search-queries';
import sidebar from '@/store/modules/sidebar';
import snippets from '@/store/modules/snippets';
import snippetsFilter from '@/store/modules/snippets-filter';
import stopWords from '@/store/modules/stop-words';
import storyteller from '@/store/modules/storyteller';
import storytellerActionPlans from '@/store/modules/storyteller-action-plans';
import surveyMonkey from '@/store/modules/survey-monkey';
import surveys from '@/store/modules/surveys';
import swotChart from '@/store/modules/swot-chart';
import themes from '@/store/modules/themes';
import themesBuilder from '@/store/modules/themes-builder';
import themesChart from '@/store/modules/themes-chart';
import themesFilter from '@/store/modules/themes-filter';
import themesGenerated from '@/store/modules/themes-generated';
import themesIntensity from '@/store/modules/themes-intensity';
import themesTable from '@/store/modules/themes-table';
import timeSeries from '@/store/modules/time-series';
import toast from '@/store/modules/toast';
import usage from '@/store/modules/usage';
import user from '@/store/modules/user';
import valueAtRisk from '@/store/modules/value-at-risk';
import workspaces from '@/store/modules/workspaces';
import workspacesDatasetPermissions from '@/store/modules/workspaces-dataset-permissions';
import workspaceSettings from '@/store/modules/workspace-settings';
import zendesk from '@/store/modules/zendesk';

export default {
  acknowledgements,
  benchmark,
  chartOptions,
  datasetManagementTabs,
  datasets,
  datasetsDelegator,
  datasetsImport,
  datasetsInsights,
  datasetsProgress,
  datasetsTag,
  emotionChart,
  globalStopWords,
  invoices,
  journey,
  layout,
  maintenance,
  modal,
  network,
  onboarding,
  organisation,
  pagedModal,
  payment,
  poll,
  qualtrics,
  registration,
  revenueAtRiskTemplate,
  savedActions,
  savedActionsBuilder,
  search,
  searchFilters,
  searchQueries,
  sidebar,
  snippets,
  snippetsFilter,
  stopWords,
  storyteller,
  storytellerActionPlans,
  surveyMonkey,
  surveys,
  swotChart,
  themes,
  themesBuilder,
  themesChart,
  themesFilter,
  themesGenerated,
  themesIntensity,
  themesTable,
  timeSeries,
  toast,
  usage,
  user,
  valueAtRisk,
  workspaces,
  workspacesDatasetPermissions,
  workspaceSettings,
  zendesk,
};
