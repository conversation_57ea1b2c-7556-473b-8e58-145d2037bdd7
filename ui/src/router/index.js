import Vue from 'vue';
import Router from 'vue-router';

import Route from '@/enum/route';

const AccountAuthenticationView = () => {
  return import(/* webpackChunkName: "account_authentication" */ '@/views/AccountAuthenticationView');
};
const AccountBillingView = () => {
  return import(/* webpackChunkName: "account_billing" */ '@/views/AccountBillingView');
};
const AccountDetailsView = () => {
  return import(/* webpackChunkName: "account_details" */ '@/views/AccountDetailsView');
};
const AccountInvoicesView = () => {
  return import(/* webpackChunkName: "account_invoices" */ '@/views/AccountInvoicesView');
};
const AccountPasswordView = () => {
  return import(/* webpackChunkName: "account_password" */ '@/views/AccountPasswordView');
};
const AccountSubscriptionsView = () => {
  return import(/* webpackChunkName: "account_subscriptions" */ '@/views/AccountSubscriptionsView');
};
const AppView = () => {
  return import(/* webpackChunkName: "app_view" */ '@/views/AppView');
};
const CommentsView = () => {
  return import(/* webpackChunkName: "comments_view" */ '@/views/CommentsView');
};
const DataImportView = () => {
  return import(/* webpackChunkName: "data_view" */ '@/views/DataImportView');
};
const JourneyView = () => {
  return import(/* webpackChunkName: "journey_view" */ '@/views/JourneyView');
};
const DatasetsView = () => {
  return import(/* webpackChunkName: "datasets" */ '@/views/DatasetsView');
};
const LoginView = () => {
  return import(/* webpackChunkName: "login" */ '@/views/LoginView');
};
const ThemeAnalysisView = () => {
  return import(/* webpackChunkName: "themes" */ '@/views/ThemeAnalysisView');
};
const EmotionAnalysisView = () => {
  return import(/* webpackChunkName: "results" */ '@/views/EmotionAnalysisView');
};
const ResetPasswordView = () => {
  return import(/* webpackChunkName: "reset_password" */ '@/views/ResetPasswordView');
};
const RevenueAtRiskTemplateView = () => {
  return import(/* webpackChunkName: "revenue_at_risk_template" */ '@/views/RevenueAtRiskTemplateView');
};
const SetPasswordView = () => {
  return import(/* webpackChunkName: "set_password" */ '@/views/SetPasswordView');
};
const SignUpView = () => {
  return import(/* webpackChunkName: "sign_up" */ '@/views/SignUpView');
};
const SignUpCompleteView = () => {
  return import(/* webpackChunkName: "sign_up_complete" */ '@/views/SignUpCompleteView');
};
const SignUpInviteView = () => {
  return import(/* webpackChunkName: "sign_up_invite" */ '@/views/SignUpInviteView');
};
const StorytellerView = () => {
  return import(/* webpackChunkName: "reset_password" */ '@/views/StorytellerView');
};
const SwotAnalysisView = () => {
  return import(/* webpackChunkName: "swot" */ '@/views/SwotAnalysisView');
};
const TrialExpiredView = () => {
  return import(/* webpackChunkName: "trial_expired" */ '@/views/TrialExpiredView');
};
const UploadView = () => {
  return import(/* webpackChunkName: "upload" */ '@/views/UploadView');
};
const UploadsView = () => {
  return import(/* webpackChunkName: "uploads" */ '@/views/UploadsView');
};
const ValueAtRiskView = () => {
  return import(/* webpackChunkName: "value_at_risk" */ '@/views/ValueAtRiskView');
};
const VerifyView = () => {
  return import(/* webpackChunkName: "verify" */ '@/views/VerifyView');
};
const WorkspaceSelectionView = () => {
  return import(/* webpackChunkName: "workspace_selection" */ '@/views/WorkspaceSelectionView');
};
const WorkspaceSettingsView = () => {
  return import(/* webpackChunkName: "workspace_settings" */ '@/views/WorkspaceSettingsView');
};
const ZendeskLoginView = () => {
  return import(/* webpackChunkName: "zendesk_login" */ '@/views/ZendeskLoginView');
};

Vue.use(Router);

export default new Router({
  mode: 'history',
  routes: [
    {
      path: '*',
      redirect: '/',
    },
    {
      path: '/',
      component: AppView,
      redirect: '/datasets',
      children: [
        {
          path: '/account/authentication',
          name: Route.ACCOUNT_AUTHENTICATION,
          component: AccountAuthenticationView,
          meta: {
            isAccountPage: true,
            title: 'Emotics - Account 2FA',
          },
        },
        {
          path: '/account/billing',
          name: Route.ACCOUNT_BILLING,
          component: AccountBillingView,
          meta: {
            isAccountPage: true,
            isAdmin: true,
            title: 'Emotics - Account Billing',
          },
        },
        {
          path: '/account/details',
          name: Route.ACCOUNT_DETAILS,
          component: AccountDetailsView,
          meta: {
            isAccountPage: true,
            title: 'Emotics - Account Details',
          },
        },
        {
          path: '/account/invoices',
          name: Route.ACCOUNT_INVOICES,
          component: AccountInvoicesView,
          meta: {
            isAccountPage: true,
            isAdmin: true,
            title: 'Emotics - Account Invoices',
          },
        },
        {
          path: '/account/password',
          name: Route.ACCOUNT_PASSWORD,
          component: AccountPasswordView,
          meta: {
            isAccountPage: true,
            title: 'Emotics - Account Password',
          },
        },
        {
          path: '/account/subscriptions',
          name: Route.ACCOUNT_SUBSCRIPTIONS,
          component: AccountSubscriptionsView,
          meta: {
            isAccountPage: true,
            isAdmin: true,
            title: 'Emotics - Account Subscription',
          },
        },
        {
          path: '/comments',
          name: Route.COMMENTS,
          component: CommentsView,
          meta: {
            title: 'Emotics - Comments',
          },
        },
        {
          path: '/data',
          name: Route.DATA_IMPORT,
          component: DataImportView,
          meta: {
            title: 'Emotics - Data Import',
          },
        },
        {
          path: '/datasets',
          name: Route.DATASETS,
          component: DatasetsView,
          meta: {
            title: 'Emotics - Datasets',
          },
        },
        {
          path: '/emotion-analysis',
          name: Route.EMOTION_ANALYSIS,
          component: EmotionAnalysisView,
          meta: {
            title: 'Emotics - Emotion Analysis',
          },
        },
        {
          path: '/journey',
          name: Route.JOURNEY,
          component: JourneyView,
          meta: {
            title: 'Emotics - Journey Mapping',
          },
        },
        {
          path: '/swot-analysis',
          name: Route.SWOT_ANALYSIS,
          component: SwotAnalysisView,
          meta: {
            title: 'Emotics - SWOT Analysis',
          },
        },
        {
          path: '/theme-analysis',
          name: Route.THEME_ANALYSIS,
          component: ThemeAnalysisView,
          meta: {
            title: 'Emotics - Theme Analysis',
          },
        },
        {
          path: '/storyteller',
          name: Route.STORYTELLER,
          component: StorytellerView,
          meta: {
            title: 'Emotics - Storyteller',
          },
        },
        {
          path: '/trial-expired',
          name: Route.TRIAL_EXPIRED,
          component: TrialExpiredView,
          meta: {
            title: 'Emotics - Trial Expired',
          },
        },
        {
          path: '/upload',
          name: Route.UPLOAD,
          component: UploadView,
          meta: {
            title: 'Emotics - Upload',
          },
        },
        {
          path: '/uploads',
          name: Route.UPLOADS,
          component: UploadsView,
          meta: {
            title: 'Emotics - Uploads',
          },
        },
        {
          path: '/revenue-at-risk',
          name: Route.REVENUE_AT_RISK,
          component: ValueAtRiskView,
          meta: {
            title: 'Emotics - Revenue at Risk',
          },
        },
        {
          path: '/revenue-at-risk-template',
          name: Route.REVENUE_AT_RISK_TEMPLATE,
          component: RevenueAtRiskTemplateView,
          meta: {
            title: 'Emotics - Revenue at Risk Template',
          },
        },
        {
          path: '/workspace-selection',
          name: Route.WORKSPACE_SELECTION,
          component: WorkspaceSelectionView,
          meta: {
            title: 'Emotics - Workspace Selection',
          },
        },
        {
          path: '/workspace-settings',
          name: Route.WORKSPACE_SETTINGS,
          component: WorkspaceSettingsView,
          meta: {
            title: 'Emotics - Workspace Settings',
          },
        },
      ],
    },
    {
      path: '/login',
      name: Route.LOGIN,
      component: LoginView,
      meta: {
        isLoginPage: true,
        title: 'Emotics - Login',
      },
    },
    {
      path: '/login/sso-google',
      name: Route.LOGIN_SSO_GOOGLE,
      component: LoginView,
      meta: {
        isLoginPage: true,
        title: 'Emotics - Login',
      },
    },
    {
      path: '/login/sso-microsoft',
      name: Route.LOGIN_SSO_MICROSOFT,
      component: LoginView,
      meta: {
        isLoginPage: true,
        title: 'Emotics - Login',
      },
    },
    {
      path: '/login/sso-amazon',
      name: Route.LOGIN_SSO_AMAZON,
      component: LoginView,
      meta: {
        isLoginPage: true,
        title: 'Emotics - Login',
      },
    },
    {
      path: '/reset-password',
      name: Route.RESET_PASSWORD,
      component: ResetPasswordView,
      meta: {
        isLoginPage: true,
        title: 'Emotics - Forgot Password',
      },
    },
    {
      path: '/set-password',
      name: Route.SET_PASSWORD,
      component: SetPasswordView,
      meta: {
        isLoginPage: true,
        title: 'Emotics - Set New Password',
      },
    },
    {
      path: '/sign-up',
      name: Route.SIGN_UP,
      component: SignUpView,
      meta: {
        isLoginPage: true,
        title: 'Emotics - Sign Up',
      },
    },
    {
      path: '/sign-up-complete',
      name: Route.SIGN_UP_COMPLETE,
      component: SignUpCompleteView,
      meta: {
        isLoginPage: true,
        title: 'Emotics - Complete Sign Up',
      },
    },
    {
      path: '/sign-up-invite',
      name: Route.SIGN_UP_INVITE,
      component: SignUpInviteView,
      meta: {
        isLoginPage: true,
        title: 'Emotics - Sign Up Invitation',
      },
    },
    {
      path: '/verify',
      name: Route.VERIFY,
      component: VerifyView,
      meta: {
        isLoginPage: true,
        title: 'Emotics - Verify',
      },
    },
    {
      path: '/sso/zendesk/login',
      name: Route.ZENDESK_LOGIN,
      component: ZendeskLoginView,
      meta: {
        isRedirect: true,
        title: 'Emotics - Zendesk',
      },
    },
  ],
});
