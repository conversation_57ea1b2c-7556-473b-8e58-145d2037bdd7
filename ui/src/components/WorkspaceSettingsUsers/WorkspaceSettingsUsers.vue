<template>
  <section class="workspace-settings-users">
    <div class="wrapper">
      <section class="header">
        <section class="title">
          <section class="left">Workspace Users</section>
          <section v-if="isEditor" class="right" @click="onClickManageCustomGroups">
            <i class="fa-regular fa-users icon" />
            Manage Custom Groups
          </section>
        </section>
        <section v-if="isAdmin" class="invite">
          <section class="invite-input">
            <input
              v-model="invitingEmail"
              class="invite-email"
              placeholder="Invite users - type <EMAIL>"
              @focus="onFocusEmailInput"
            />
            <i v-if="emailInvalidErrorShow" class="fa-solid fa-circle-exclamation icon-email-error"></i>
            <section
              class="invite-role"
              :class="{ focus: inviteRoleFocus }"
              @click="onClickInviteRole"
              v-click-outside-handler="{
                handler: 'onClickOutside',
              }"
            >
              {{ textInvitingRole }}
              <i class="fa fa-caret-down icon"></i>
            </section>
          </section>
          <section class="invite-submit-btn" @click="onClickSubmit">
            Submit
          </section>
        </section>
        <span :class="{ hide: !emailInvalidErrorShow }" class="email-note">{{emailInvalidError}}</span>
      </section>
      <section class="body">
        <!-- members -->
        <section class="list list-members" v-if="membersList.length">
          <section class="headers">
            <span class="header-item">{{ membersHeader }}</span>
            <span class="header-item">Status</span>
          </section>
          <section class="items">
            <workspace-users-member-item
              class="item member-item"
              v-for="item in membersList"
              :key="item.email"
              :member-item="item"
              @delete="onDeleteMember"
            />
          </section>
        </section>
        <!-- pending invites -->
        <section class="list list-pending-invites" v-if="pendingInvitesList.length">
          <section class="headers">
            <span class="header-item">Pending Invites</span>
            <span class="header-item"></span>
          </section>
          <section class="items">
            <workspace-users-pending-invite-item
              class="item pending-invite-item"
              v-for="item in pendingInvitesList"
              :key="item.email"
              :member-item="item"
              @delete="onDeleteInvite"
            />
          </section>
        </section>
      </section>
      <section class="footer">
        <span></span>
        <span :class="{ hide: !emailIncompleteErrorShow }" class="email-note">{{emailIncompleteError}}</span>
        <loading-blocks-overlay v-if="editing" />
        <base-button v-else class="done-btn" size="small" @click="onClickDone">Done</base-button>
      </section>
      <!-- invite user-role dropdown -->
      <workspace-invite-user-roles
        :class="{ hide: !inviteRoleFocus }"
        :invitingRole="invitingRole"
        @onSelect="selectInvitingRole"
      />
    </div>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import clickOutsideHandler from '@/directives/click-outside-handler';
import EmailValidator from '@/services/EmailValidator';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import Route from '@/enum/route';
import WorkspaceErrorToast from '@/components/Toasts/WorkspaceErrorToast';
import WorkspaceInviteUserRole from '@/enum/workspace-invite-user-role';
import WorkspaceInviteUserRoles from '@/components/WorkspaceUsersManagement/WorkspaceInviteUserRoles';
import WorkspaceInviteUserToast from '@/components/Toasts/WorkspaceInviteUserToast';
import WorkspaceUsersMemberItem from '@/components/WorkspaceUsersManagement/WorkspaceUsersMemberItem';
import WorkspaceUsersPendingInviteItem from '@/components/WorkspaceUsersManagement/WorkspaceUsersPendingInviteItem';

import { workspaceApi } from '@/services/api';

export default {
  name: 'workspace-settings-users',

  components: {
    BaseButton,
    LoadingBlocksOverlay,
    WorkspaceInviteUserRoles,
    WorkspaceUsersMemberItem,
    WorkspaceUsersPendingInviteItem,
  },

  directives: {
    clickOutsideHandler,
  },

  data() {
    return {
      editing: false,
      emailIncompleteError: 'Please submit the email address using the button above',
      emailIncompleteErrorShow: false,
      emailInvalidError: 'Please enter a valid email address',
      emailInvalidErrorShow: false,
      inviteRoleFocus: false,
      invitingEmail: '',
      invitingRole: WorkspaceInviteUserRole.VIEWER,
      membersList: [],
    };
  },

  computed: {
    ...mapGetters('user', ['isAdmin', 'isEditor']),

    ...mapState('workspaces', [
      'editingWorkspaceMembersList',
      'emailsToDisinvite',
      'emailToInvite',
      'selectedWorkspace',
    ]),

    emailValidator() {
      return new EmailValidator(this.invitingEmail);
    },

    hasError() {
      if (!this.invitingRole) {
        return true;
      }

      return !this.invitingEmail?.trim().length || !this.emailValidator.constraintsPassed;
    },

    membersHeader() {
      const size = this.membersList.length;
      return `${size} Workspace ${size > 1 ? 'Members' : 'Member'}`;
    },

    pendingInvitesList() {
      return this.selectedWorkspace?.pendingInvites || [];
    },

    textInvitingRole() {
      return this.invitingRole?.name || '';
    },
  },

  mounted() {
    const { owner, administrators, editors, viewers } = this.selectedWorkspace;
    this.membersList.push(owner, ...administrators, ...editors, ...viewers);
  },

  beforeDestroy() {
    this.setEditingWorkspaceMembersList({ members: [] });
    this.setEmailsToDisinvite({ emails: [] });
  },

  methods: {
    ...mapActions('toast', ['add']),

    ...mapActions('workspaces', ['setSelectedWorkspace']),

    ...mapActions('workspaces', [
      'setEditingWorkspaceMembersList',
      'setEmailsToDisinvite',
      'setEmailToInvite',
      'setShowWorkspaceCustomGroups',
    ]),

    async onClickDone() {
      if (this.invitingEmail) {
        this.emailIncompleteErrorShow = true;
        return;
      }
      this.editing = true;
      if (this.emailsToDisinvite.length) {
        await workspaceApi.disinviteUser(this.selectedWorkspace.id, this.emailsToDisinvite);
      }
      this.editing = false;
      if (this.editingWorkspaceMembersList.length) {
        const response = await workspaceApi.updateUsers(this.selectedWorkspace.id, this.editingWorkspaceMembersList);

        if (response.error) {
          this.add({
            toast: {
              component: WorkspaceErrorToast,
              id: 'workspace-error-toast',
            },
          });
          return;
        }
      }
      await this.reloadWorkspace();
    },

    onClickInviteRole() {
      this.inviteRoleFocus = !this.inviteRoleFocus;
    },

    onClickManageCustomGroups() {
      if (this.$route.name !== Route.DATASETS) this.$router.push({ name: Route.DATASETS });
      this.setShowWorkspaceCustomGroups({ value: true });
    },

    onClickOutside() {
      this.inviteRoleFocus = false;
    },

    async onClickSubmit() {
      if (this.hasError) {
        this.emailInvalidErrorShow = true;
        return;
      }
      this.emailIncompleteErrorShow = false;

      this.setEmailToInvite({ email: this.invitingEmail });

      const response = await workspaceApi.inviteUser(
        this.selectedWorkspace.id,
        this.emailToInvite.trim(),
        this.invitingRole.name,
      );
      if (response.error) {
        this.add({
          toast: {
            component: WorkspaceErrorToast,
            id: 'workspace-error-toast',
          },
        });

        return;
      }

      this.add({
        toast: {
          component: WorkspaceInviteUserToast,
          id: 'workspace-invite-new-user',
        },
      });
      this.invitingEmail = '';

      await this.reloadWorkspace();
    },

    onDeleteInvite(invite) {
      // TODO: Change to use action here rather update directly to vuex state
      this.selectedWorkspace.pendingInvites = this.selectedWorkspace.pendingInvites.filter(i => i.email !== invite.email);
    },

    onDeleteMember(member) {
      this.membersList = this.membersList.filter(i => i.id !== member.id);
    },

    onFocusEmailInput() {
      this.emailInvalidErrorShow = false;
      this.emailIncompleteErrorShow = false;
    },

    async reloadWorkspace() {
      const workspace = await workspaceApi.getWorkspace(this.selectedWorkspace.id);
      await this.setSelectedWorkspace({ workspace });
    },

    selectInvitingRole(role) {
      this.invitingRole = role;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.workspace-settings-users {
  height: 100%;
  overflow-y: auto;
  padding: 1.8rem 1.5rem 3rem 1.5rem;
  width: 100%;

  .wrapper {
    @include flex("block", "column", "start", "start");

    position: relative;
    width: 600px;

    .email-note {
      color: rgba(203, 21, 9, 0.8);
      font-size: $font-size-xs;
      padding: 0.3rem 0.5rem;

      &.hide {
        visibility: hidden;
      }
    }

    .header {
      @include flex('block', 'column', 'center', 'start');
      @include rigid;

      border-bottom: $border-light solid $border-color;
      width: 100%;

      .invite {
        @include flex('block', 'row', 'between', 'center');
        margin-top: 1rem;
        width: 100%;

        .invite-input {
          @include flex('block', 'row', 'start', 'center');
          border: 0.5px solid rgba(115, 98, 183, 1);
          border-radius: 2px;
          width: 100%;

          .icon-email-error {
            color: rgba(203, 21, 9, 0.8);
            margin-right: 0.5rem;
          }

          .invite-email {
            background: none;
            border: none;
            font-size: $font-size-xs;
            padding: 0 0.5rem;
            width: 100%;

            &:active, &:focus {
              border: none;
              outline: none;
            }
          }

          .invite-role {
            @include flex('block', 'row', 'center', 'center');
            border-left: 1px solid rgba(115, 98, 183, 1);
            color: #2D1757;
            cursor: pointer;
            font-size: $font-size-xxs;
            font-weight: $font-weight-bold;
            padding: 0.5rem 1rem;

            .icon {
              margin-left: 0.5rem;
              transition: transform $interaction-transition-time;
            }

            &.focus {
              .icon {
                transform: rotateX(180deg);
              }
            }
          }
        }

        .invite-submit-btn {
          @include flex('block', 'row', 'end', 'center');
          background: #5F52C5;
          border-radius: 2px;
          color: #ffffff;
          cursor: pointer;
          font-size: 0.65rem;
          font-weight: $font-weight-bold;
          text-transform: uppercase;
          margin-left: 0.5rem;
          padding: 0.5rem 1.2rem;
          height: 100%;
        }
      }

      .title {
        @include flex('block', 'row', 'between', 'center');
        width: 100%;

        .left {
          color: #2D1757;
          font-size: $font-size-sm;
          font-weight: $font-weight-bold;
        }

        .right {
          @include flex('block', 'row', 'end', 'center');
          border: 1px solid rgba(75, 114, 240, 0.15);
          border-radius: 2px;
          color: rgba(0, 99, 255, 1);
          cursor: pointer;
          font-size: $font-size-xxs;
          font-weight: $font-weight-bold;
          padding: 0.5rem;
          text-transform: uppercase;

          &:hover {
            border: 1px solid rgba(75, 114, 240, 0.45);
          }

          .icon {
            font-size: $font-size-xs;
            margin-right: 0.5rem;
          }
        }
      }
    }

    .loading-blocks-overlay {
      height: 28px;
      margin: 0;
    }

    .body {
      @include flex('block', 'column', 'start', 'start');
      @include scrollbar-thin;

      width: 100%;

      .list {
        @include flex('block', 'column', 'start', 'start');

        padding: 1rem 0;
        border-bottom: 1px solid #dee1e4;
        width: 100%;

        &:first-child {
          padding-top: 1.5rem;
        }

        &:last-child {
          border-bottom: none;
          padding-bottom: 1.5rem;
        }

        .headers {
          @include flex('block', 'row', 'between', 'center');
          color: rgba(95, 82, 197, 1);
          font-size: $font-size-xs;
          font-weight: $font-weight-bold;
          text-transform: uppercase;
          width: 100%;
        }

        .items {
          width: 100%;

          .item {
            border-bottom: 1px solid rgba(242, 241, 251, 1);
            padding: 1rem 0;

            &:first-child {
              padding-top: 1rem;
            }

            &:last-child {
              border-bottom: none;
              padding-bottom: 0;
            }
          }
        }
      }
    }

    .footer {
      @include flex('block', 'row', 'space-between', 'center');

      border-top: $border-light solid $border-color;
      padding: 1.5rem 0;
      width: 100%;

      .base-button {
        font-size: 0.65em;
        font-weight: 600;
        text-transform: uppercase;

        &.done-btn {
          background: #2D1757;
          padding: 0.5rem 1.5rem;
        }
      }
    }

    .workspace-invite-user-roles {
      position: absolute;
      right: 92px;
      top: 80px;
      z-index: 2;

      &.hide {
        display: none;
      }
    }
  }
}
</style>
