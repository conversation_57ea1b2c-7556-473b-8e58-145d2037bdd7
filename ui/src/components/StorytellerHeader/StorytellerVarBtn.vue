<template>
  <section class="storyteller-var-btn">
    <base-button v-if="dataset.rarCalculated" class="btn btn-calculate" size="small" @click="onClick">
      <i class="fa-light fa-calculator icon-var" />
      <span>Revenue at Risk Calculated</span>
    </base-button>
    <base-button v-else class="btn btn-not-calculate" size="small" @click="onClick">
      <i class="fa-light fa-xmark icon-var" />
      <span>Revenue at Risk Not Calculated</span>
    </base-button>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import Route from '@/enum/route';

export default {
  name: 'storyteller-var-btn',

  components: {
    BaseButton,
  },

  computed: {
    ...mapGetters('datasets', {
      getDataset: 'get',
    }),

    ...mapState('datasets', {
      datasetId: 'active',
    }),

    dataset() {
      return this.getDataset(this.datasetId);
    },
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('valueAtRisk', ['setPreviousRoute']),

    onClick() {
      this.setPreviousRoute({ route: Route.STORYTELLER });
      this.$router.push({
        name: Route.REVENUE_AT_RISK,
        query: {
          id: this.dataset.id,
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-var-btn {
  @include flex("block", "row", "start", "center");

  .btn {
    background-color: #FFF;
    border-radius: $border-radius-small;
    border: 1px solid rgba(124, 124, 124, 0.2);
    color: $nps-blue;
    font-size: $font-size-xxs;
    font-weight: $font-weight-bold;
    padding: 0.3rem 0.6rem;

    .icon-var {
      margin-right: 0.3rem;
    }

    &:hover, &:focus {
      background-color: #FFF;
      border-color: rgba(124, 124, 124, 1);
    }

    &.btn-not-calculate {
      background-color: #F4F4F4;
    }
  }
}
</style>
