<template>
  <section class="datasets-item-rar-dropdown-footer">
    <base-button class="cancel-btn" colour="light" size="small" type="link" @click="onClickCancel">Cancel</base-button>
    <loading-blocks-overlay size="small" v-if="proceeding" />
    <base-button v-else class="done-btn" size="small" @click="onClickDone">Done</base-button>
  </section>
</template>

<script>
import BaseButton from '@/components/Base/BaseButton';
import DatasetsRequestV0 from '@/services/request/DatasetsRequestV0';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import ValueAtRiskRequest from '@/services/request/ValueAtRiskRequest';

export default {
  name: 'datasets-item-rar-dropdown-footer',

  components: {
    BaseButton,
    LoadingBlocksOverlay,
  },

  props: {
    localDataset: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      proceeding: false,
    };
  },

  methods: {
    onClickCancel() {
      this.$emit('onClose');
    },

    async onClickDone() {
      this.proceeding = true;

      await ValueAtRiskRequest.applyRarTemplate(this.localDataset.datasetRevenueAtRisk.templateId);
      await DatasetsRequestV0.getDatasets();

      this.$emit('onClose');
      this.proceeding = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.datasets-item-rar-dropdown-footer {
  @include flex('block', 'row', 'between', 'center');

  padding: 1rem;

  .base-button {
    font-size: 0.65em;
    font-weight: 600;
    text-transform: uppercase;

    &.cancel-btn {
      padding: 0.5rem 1rem 0.5rem 0;
    }

    &.done-btn {
      background: #2D1757;
      padding: 0.5rem 1.5rem;

      &:hover {
        background-color: lighten(#2D1757, 10%);
      }
    }
  }

  .loading-blocks-overlay {
    height: 28px;
  }
}
</style>
