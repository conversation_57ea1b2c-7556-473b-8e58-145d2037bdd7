<template>
  <section class="datasets-item-rar-btn">
    <div class="btn"
      :class="{ 'not-calculate': !this.dataset.datasetRevenueAtRisk }"
      @click="onClick"
      v-click-outside-handler="{ handler: 'onClose' }"
    >
      <div class="text">
        <i class="fa-light fa-calculator icon"></i>
        <span class="name">{{name}}</span>
      </div>
      <div class="dropdown-icon" >
        <i class="fa fa-caret-down" />
      </div>
    </div>
    <datasets-item-rar-dropdown v-if="open" :id="id" @onClose="open=false" />
  </section>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import DatasetsItemRarDropdown from '@/components/DatasetsItemRar/DatasetsItemRarDropdown';
import clickOutsideHandler from '@/directives/click-outside-handler';

export default {
  name: 'datasets-item-rar-btn',

  components: {
    BaseButton,
    DatasetsItemRarDropdown,
  },

  directives: {
    clickOutsideHandler,
  },

  props: {
    id: {
      type: Number,
      required: true,
    },
  },

  data() {
    return {
      open: false,
    };
  },

  computed: {
    ...mapGetters('datasets', {
      getDataset: 'get',
    }),

    dataset() {
      return this.getDataset(this.id);
    },

    name() {
      if (this.dataset.datasetRevenueAtRisk) {
        return this.dataset.datasetRevenueAtRisk.name;
      }

      return 'Select Revenue';
    },
  },

  methods: {
    ...mapActions('valueAtRisk', ['setDatasetId']),

    onClick() {
      this.setDatasetId({ datasetId: this.id });
      this.open = !this.open;
    },

    onClose() {
      this.open = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.datasets-item-rar-btn{
  @include flex("block", "row", "space-between", "center");

  position: relative;

  .btn {
    @include flex("block", "row", "start", "center");

    cursor: pointer;
    font-size: 10px;
    height: 1.5rem;
    min-width: 120px;
    position: relative;
    width: 120px;

    &.not-calculate {
      opacity: 0.4;
    }

    &:hover {
      opacity: 1;

      .text, .dropdown-icon {
        border-color: rgba(115, 98, 183, 1);
      }
    }

    .text {
      @include flex('inline', 'row', 'start', 'center');

      background-color: clr("white");
      border-bottom-left-radius: $border-radius-small;
      border-top-left-radius: $border-radius-small;
      border: 1px solid rgba(115, 98, 183, 0.7);
      border-right: none;
      height: 100%;
      padding-left: 2px;
      padding-right: 2px;
      position: relative;
      width: calc(100% - 1.5rem);

      .icon {
        margin-right: 3px;
      }

      .name {
        @include truncate;
      }
    }

    .dropdown-icon {
      @include panel;
      @include flex('inline', 'row', 'center', 'center');

      border-radius: 0 $border-radius-small $border-radius-small 0;
      border: 1px solid rgba(115, 98, 183, 0.7);
      box-shadow: unset;
      cursor: pointer;
      height: 100%;
      width: 1.5rem;
    }
  }
}
</style>
