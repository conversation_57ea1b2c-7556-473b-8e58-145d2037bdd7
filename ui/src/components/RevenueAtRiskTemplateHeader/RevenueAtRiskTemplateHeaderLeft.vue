<template>
  <section class="value-at-risk-template-header-left">
    <icon-arrow-back @click.stop="onClickBack" />
    <i class="fa-solid fa-calculator icon-calculator" />
    <span class="title">Revenue at Risk Calculator</span>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import IconArrowBack from '@/components/Icons/IconArrowBack';
import Route from '@/enum/route';

export default {
  name: 'value-at-risk-template-header-left',

  components: {
    IconArrowBack,
  },

  computed: {
    ...mapState('workspaces', ['selectedWorkspace']),
  },

  methods: {
    onClickBack() {
      this.$router.push({
        name: Route.WORKSPACE_SETTINGS,
        query: {
          id: this.selectedWorkspace.id,
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.value-at-risk-template-header-left {
  @include flex("block", "row", "start", "center");

  .icon-calculator {
    margin-left: 1rem;
    margin-right: 0.4rem;
  }

  .title {
    font-weight: $font-weight-bold;
    margin-right: 0.4rem;
  }

  .adorescore-box-mini {
    margin-right: 0.4rem;
    width: 2rem;

    .score {
      font-size: 12px;
    }
  }
}
</style>
