<template>
  <section class="revenue-at-risk-template-update-toast">
    <div class="toast">
      <div class="left">
        <check-icon class="icon"/>
        <span class="text">Revenue at risk template updated.</span>
      </div>
      <div class="right">
        <base-button class="dismiss" colour="light" @click="onDismiss">Dismiss</base-button>
      </div>
    </div>
  </section>
</template>

<script>
import { CheckIcon } from 'vue-feather-icons';
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import Toast from '@/components/Mixins/Toast';

export default {
  name: 'revenue-at-risk-template-update-toast',

  components: {
    BaseButton,
    CheckIcon,
  },

  mixins: [Toast],

  beforeDestroy() {
    this.remove({ id: 'value-at-risk-update-toast' });
  },

  created() {
    this.countWatcher = setInterval(() => {
      this.count -= 1;
      if (this.count === 0) {
        clearInterval(this.countWatcher);
        this.close();
      }
    }, 1000);
  },

  data() {
    return {
      count: 3,
      countWatcher: null,
    };
  },

  methods: {
    ...mapActions('toast', ['remove']),

    onDismiss() {
      this.remove({ id: 'value-at-risk-update-toast' });
      this.close();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins/index";

.revenue-at-risk-template-update-toast {
  @include flex("block", "row", "end", "start");

  .toast {
    @include toast;

    border-radius: $border-radius-medium;
    padding: 1rem 1.5rem;

    .base-button {
      padding: 0.6rem 1.6rem;
    }

    .left {
      @include flex("block", "row", "start", "center");

      margin-right: 4rem;

      span {
        font-weight: $font-weight-normal;
        margin-left: 0.3rem;
      }
    }
  }
}
</style>
