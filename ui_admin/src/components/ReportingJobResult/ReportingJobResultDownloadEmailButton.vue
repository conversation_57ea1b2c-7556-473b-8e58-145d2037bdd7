<template>
  <base-button
      class="download"
      @click="downloadHtml"
      :size="'small'" :colour="'dark'"
  >
    Download HTML
  </base-button>
</template>

<script>
import { mapActions } from 'vuex';
import BaseButton from '@/components/Base/BaseButton';
import ReportingJobResultPreviewEmailModal from '@/components/ReportingJobResult/ReportingJobResultPreviewEmailModal';

export default {
  name: 'ReportingJobResultDownloadEmailButton',

  components: {
    BaseButton,
  },

  props: {
    html: {
      type: String,
      required: true,
    },
  },

  methods: {
    ...mapActions('modal', ['setModalAndProps']),

    downloadHtml() {
      if (!this.html) return;

      const filename = `${this.reportingJobResult.datasetOriginalName}.html`;
      const blob = new Blob([this.html], { type: 'text/html' });
      const url = window.URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    },
  },
};
</script>

<style lang="scss" scoped>
.icon {
  margin-right: 0.2em;
}
</style>
