---
# Confirm assumptions with Adoreboard!
# Assume: ONLY deploy from MAIN.
# Assume: DO NOT deploy feature branches.
# Assume: Feature branches are built but not deployed.
image: maven:3.9.9-eclipse-temurin-11

# To avoid the pipeline running indefinitely, adjust the maximum time for pipeline runs if needed in minutes
# options:
#   max-time: 120
definitions:
  caches:
    maven: ~/.m2/repository
  services:
    postgres:
      image: postgres:15
      environment:
        POSTGRES_DB: farfisa_test
        POSTGRES_USER: postgres
        POSTGRES_PASSWORD: "123456"
    docker:
      memory: 3072

pipelines:
  branches:
    '{main,release}':
      - step:
          name: "Build with Parameters"
          image: maven:3.8.6-openjdk-11
          script:
            - echo "INFO Building Farfisa with BumpType [ $BITBUCKET_BRANCH ]"

      - step:
          name: Download Artifacts Caerus
          image: maven:3.8.6-openjdk-11
          script:
            # install xmllint to update versions in pom files
            - |
              apt-get update && apt-get install -y libxml2-utils
              xmllint --version
            # TODO: Get specific caerus version in the pom to download!
            - |
              CAERUS_VERSION=$(xmllint --xpath "string(//*[local-name()='properties']/*[local-name()='caerus.version'])" pom.xml)
              echo INFO: Farfisa dependancy on Caerus is version ${CAERUS_VERSION}
            # Create folder
            - |
              mkdir caerus
              cd caerus
            # Download dependancies
            - |
              curl -L -u ${BB_AUTH_STRING} https://api.bitbucket.org/2.0/repositories/adoreboard/adoreboard-caerus/downloads/caerus-common-1.14.0.jar -o caerus-common-1.14.0.jar
              curl -L -u ${BB_AUTH_STRING} https://api.bitbucket.org/2.0/repositories/adoreboard/adoreboard-caerus/downloads/caerus-api-1.14.0.jar -o caerus-api-1.14.0.jar
              curl -L -u ${BB_AUTH_STRING} https://api.bitbucket.org/2.0/repositories/adoreboard/adoreboard-caerus/downloads/pom.xml -o pom.xml
              ls -l
          artifacts:
            - caerus/caerus-common-*.jar
            - caerus/caerus-api-*.jar
            - caerus/pom.xml

      - step:
          name: "Bump Version"
          image: maven:3.8.6-openjdk-11
          script:
            - echo "INFO Chosen bump type:" $BITBUCKET_BRANCH
            # install xmllint to update versions in pom files
            - |
              apt-get update && apt-get install -y libxml2-utils
              xmllint --version
            # Change version by major, minor or incrumentally based of release type! Currently a Jenkins drop-down entry.
            # If none of these if statements are true the version is not updated.
            - |
              if [[ "$BITBUCKET_BRANCH" == testing-* ]]; then
                OLD_VERSION=$(xmllint --xpath "//*[local-name()='project']/*[local-name()='version']/text()" pom.xml)
                mvn build-helper:parse-version versions:set -DnewVersion='${parsedVersion.majorVersion}'.'${parsedVersion.minorVersion}'.'${parsedVersion.nextIncrementalVersion}' versions:commit
                NEW_VERSION=$(xmllint --xpath "//*[local-name()='project']/*[local-name()='version']/text()" pom.xml)
                echo "INFO: Bumping version from [ $OLD_VERSION ] to [ $NEW_VERSION ]"
              elif [ "$BITBUCKET_BRANCH" == "patch" ]; then
                mvn build-helper:parse-version versions:set -DnewVersion="\${parsedVersion.majorVersion}.\${parsedVersion.minorVersion}.\${parsedVersion.nextIncrementalVersion}" versions:commit
              elif [ "$BITBUCKET_TAG" == "minor" ]; then
                mvn build-helper:parse-version versions:set -DnewVersion="\${parsedVersion.majorVersion}.\${parsedVersion.nextMinorVersion}.0" versions:commit
              elif [ "$BITBUCKET_TAG" == "major" ]; then
                mvn build-helper:parse-version versions:set -DnewVersion="\${parsedVersion.nextMajorVersion}.0.0" versions:commit
              fi
            # Commit version change to git
            # TODO: Remove echo from command once branching strategy and version updating method finalised!
            - |
              echo "INFO: updating GIT"
              git status
              echo git config --global push.default simple
              echo git config --global user.name "Pipelines Adorebot"
              echo git config --global user.email <EMAIL>
              echo git add -u
              echo git tag -a "v$NEW_VERSION" -m "Release version v$NEW_VERSION from $BITBUCKET_BRANCH"
              echo git commit -m "Bumping version from [ $OLD_VERSION ] to [ $NEW_VERSION ]"
              echo git push origin $BITBUCKET_BRANCH --follow-tags

      - step:
          name: "Clone & Compile Farfisa"
          image: maven:3.8.6-openjdk-11
          caches:
            - maven
          services:
            - postgres
          script:
            # Location and files are start
            - ls
            # Install caerus-common to mvn local
            - |
              mvn install:install-file \
              -Dfile=caerus/caerus-common-1.14.0.jar \
              -DpomFile=caerus/pom.xml \
              -DgroupId=com.adoreboard \
              -DartifactId=caerus-common \
              -Dversion=1.14.0 \
              -Dpackaging=jar
            # Install caerus-api to mvn local
            - |
              mvn install:install-file \
              -Dfile=caerus/caerus-api-1.14.0.jar \
              -DpomFile=caerus/pom.xml \
              -DgroupId=com.adoreboard \
              -DartifactId=caerus-api \
              -Dversion=1.14.0 \
              -Dpackaging=jar
            # Confirm dependancies installed
            - ls /root/.m2/repository/com/
            - ls /root/.m2/repository/com/adoreboard/*/*
            # set up postgre db for tests
            - |
              export DB_HOST=localhost
              export DB_PORT=5432
              export DB_NAME=farfisa_test
              export DB_USER=postgres
              export DB_PASSWORD="123456"
            # Install psql + netcat
            - |
              apt-get update
              apt-get install -y postgresql-client
              apt-get install -y netcat
              psql --version
            # Check port is open and accepting connections
            - nc -z "$DB_HOST" "$DB_PORT"
            # Wait for PostgreSQL to be ready before running tests
            - until psql -h "$DB_HOST" -U "$DB_USER" -c '\q'; do >&2 echo "INFO Postgres is unavailable - sleeping"; sleep 1; done
            # Compile farfisa
            - mvn clean compile
            # Deploy farfisa-common
            - |
              mvn install -Dtest=NotExists -Dsurefire.failIfNoSpecifiedTests=false
              ls */*/*
            # Upload to Downloads
            - |
              curl -v -X POST -u ${BB_AUTH_STRING} https://api.bitbucket.org/2.0/repositories/adoreboard/adoreboard-farfisa/downloads -F files=@farfisa-common/target/farfisa-common-19.4.0-tests.jar
              curl -v -X POST -u ${BB_AUTH_STRING} https://api.bitbucket.org/2.0/repositories/adoreboard/adoreboard-farfisa/downloads -F files=@farfisa-common/target/farfisa-common-19.4.0.jar
              curl -v -X POST -u ${BB_AUTH_STRING} https://api.bitbucket.org/2.0/repositories/adoreboard/adoreboard-farfisa/downloads -F files=@farfisa-calculator/farfisa-calculator-common/pom.xml
              curl -v -X POST -u ${BB_AUTH_STRING} https://api.bitbucket.org/2.0/repositories/adoreboard/adoreboard-farfisa/downloads -F files=@farfisa-calculator/farfisa-calculator-server/pom.xml       
              curl -v -X POST -u ${BB_AUTH_STRING} https://api.bitbucket.org/2.0/repositories/adoreboard/adoreboard-farfisa/downloads -F files=@farfisa-calculator/farfisa-calculator-common/target/farfisa-calculator-common-19.4.0.jar
              curl -v -X POST -u ${BB_AUTH_STRING} https://api.bitbucket.org/2.0/repositories/adoreboard/adoreboard-farfisa/downloads -F files=@farfisa-api/target/farfisa-api-19.4.0.war
              curl -v -X POST -u ${BB_AUTH_STRING} https://api.bitbucket.org/2.0/repositories/adoreboard/adoreboard-farfisa/downloads -F files=@farfisa-processor/target/farfisa-processor-19.4.0.war
              curl -v -X POST -u ${BB_AUTH_STRING} https://api.bitbucket.org/2.0/repositories/adoreboard/adoreboard-farfisa/downloads -F files=@farfisa-data/target/farfisa-data-19.4.0.war
              curl -v -X POST -u ${BB_AUTH_STRING} https://api.bitbucket.org/2.0/repositories/adoreboard/adoreboard-farfisa/downloads -F files=@farfisa-calculator/farfisa-calculator-server/target/farfisa-calculator-server-19.4.0.war
          artifacts:
            - farfisa-common/target/farfisa-common-19.4.0-tests.jar
            - farfisa-common/target/farfisa-common-19.4.0.jar
            - farfisa-calculator/farfisa-calculator-common/pom.xml
            - farfisa-calculator/farfisa-calculator-server/pom.xml
            - farfisa-calculator/farfisa-calculator-common/target/farfisa-calculator-common-19.4.0.jar
            - farfisa-api/target/farfisa-api-19.4.0.war
            - farfisa-processor/target/farfisa-processor-19.4.0.war
            - farfisa-data/target/farfisa-data-19.4.0.war
            - farfisa-calculator/farfisa-calculator-server/target/farfisa-calculator-server-19.4.0.war

      - step:
          name: "Build Dockerfile Farfisa and push to ECR"
          image: amazon/aws-cli:2.27.50
          oidc: true
          max-time: 15
          services:
            - docker
          caches:
            - docker
          script:
            - set -e
            # TODO: Set REGISTRY based on branch type
            # Separate REGISTRY for testing, staging and production
            # ECR and registry name from branch
            - |
              if [ $BITBUCKET_BRANCH == "main" ]; then
                ECR=587748902937.dkr.ecr.us-east-1.amazonaws.com
                REGISTRY=adoreboard-prod-use1-ecr
              else
                ECR=587748902937.dkr.ecr.eu-west-2.amazonaws.com
                REGISTRY=adoreboard-testing-staging-euw2-ecr
              fi
            # Set image type using the branch name
            - |
              echo $BITBUCKET_BRANCH
              if [[ "$BITBUCKET_BRANCH" == *"/"* ]]; then
                # Split by "/" and take the first part
                IMG_TYPE="${BITBUCKET_BRANCH%%/*}"
              else
                # Use the whole value of BITBUCKET_BRANCH
                IMG_TYPE="$BITBUCKET_BRANCH"
              fi
              echo "IMG_TYPE="${IMG_TYPE}
            # TODO: Remove overwrite once branching strategy decided
            - IMG_TYPE=image
            # install xmllint to update versions in pom files
            - |
              apk update && apk add --no-cache libxml2-utils
              xmllint --version
              VERSION=$(xmllint --xpath "//*[local-name()='project']/*[local-name()='version']/text()" pom.xml)
              echo INFO: POM file version ${VERSION}
            # Configure aws on branch name
            - |
              if [ $BITBUCKET_BRANCH == "main" ]; then
                export AWS_REGION=us-east-1
                export AWS_ROLE_ARN=arn:aws:iam::587748902937:role/bitbucket-oidc-prod
                export AWS_WEB_IDENTITY_TOKEN_FILE=$(pwd)/web-identity-token
                echo $BITBUCKET_STEP_OIDC_TOKEN > $(pwd)/web-identity-token
              else
                export AWS_REGION=eu-west-2
                export AWS_ROLE_ARN=arn:aws:iam::587748902937:role/bitbucket-oidc
                export AWS_WEB_IDENTITY_TOKEN_FILE=$(pwd)/web-identity-token
                echo $BITBUCKET_STEP_OIDC_TOKEN > $(pwd)/web-identity-token
              fi
              unset AWS_ACCESS_KEY_ID
              unset AWS_SECRET_ACCESS_KEY
            # Loop through the Farfisa folders which have Dockerfiles
            - |
              for dir in $(find . -name 'Dockerfile' -exec dirname {} \;)
              do
                FOLDER_NAME=$(basename "$dir")
                echo INFO: Building Docker image for ${FOLDER_NAME}
                cd ${FOLDER_NAME}
                # Build image
                docker build -t ${REGISTRY}:${FOLDER_NAME}-${IMG_TYPE}-${VERSION} .
                # Tag on the image is the same as the tag of the version built.
                docker tag ${REGISTRY}:${FOLDER_NAME}-${IMG_TYPE}-${VERSION} ${REGISTRY}:${FOLDER_NAME}-${IMG_TYPE}-latest
                docker tag ${REGISTRY}:${FOLDER_NAME}-${IMG_TYPE}-${VERSION} ${ECR}/${REGISTRY}:${FOLDER_NAME}-${IMG_TYPE}-${VERSION}
                docker image ls
                # Check image exist in ECR
                echo aws ecr describe-images --repository-name ${REGISTRY} --region ${AWS_REGION} --query "imageDetails[?imageTags != null && contains(imageTags, '${FOLDER_NAME}-${IMG_TYPE}-${VERSION}')]" --output json
                OUTPUT=$(aws ecr describe-images --repository-name ${REGISTRY} --region ${AWS_REGION} --query "imageDetails[?imageTags != null && contains(imageTags, '${FOLDER_NAME}-${IMG_TYPE}-${VERSION}')]" --output json)
                echo $OUTPUT
                if [[ -z "$OUTPUT" || "$OUTPUT" == "[]" ]]; then
                  echo "INFO: Tag '$FOLDER_NAME' '$VERSION' does not exist in repository '$REGISTRY'. Continuing to push image ..."
                  # Authourise and push to ECR
                  aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${ECR}
                  docker push ${ECR}/${REGISTRY}:${FOLDER_NAME}-${IMG_TYPE}-${VERSION}
                  # Add Tag to SSM
                  aws ssm put-parameter --name "/farfisa/${FOLDER_NAME}" --type "String" --value "${FOLDER_NAME}-${IMG_TYPE}-${VERSION}" --overwrite 
              
                else
                  echo "WARN: Tag '$FOLDER_NAME' '$VERSION' exists in repository '$FOLDER_NAME'. Delete image or comment and rerun to over write"
                fi
                cd ../
              done
