<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~  Copyright 2022 Adoreboard Ltd. All rights reserved.
  ~  ADOREBOARD PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>farfisa</artifactId>
        <groupId>com.adoreboard</groupId>
        <version>19.4.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>farfisa-data</artifactId>
    <packaging>war</packaging>

    <dependencies>
        <!-- Internal dependencies -->
        <dependency>
            <groupId>com.adoreboard</groupId>
            <artifactId>farfisa-common</artifactId>
        </dependency>

        <!-- Spring -->

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>

        <!-- Other -->

        <dependency>
            <groupId>org.apache.opennlp</groupId>
            <artifactId>opennlp-tools</artifactId>
        </dependency>
        <dependency>
            <groupId>com.optimaize.languagedetector</groupId>
            <artifactId>language-detector</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.vinhkhuc</groupId>
            <artifactId>jfasttext</artifactId>
        </dependency>

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
    </dependencies>

    <!--FIXME - VU: Jenkins doesn't have RabbitMQ, and Redis; build is always failed; Enable these lines once Jenkins fixed-->
    <!--<build>-->
    <!--<plugins>-->
    <!--<plugin>-->
    <!--<groupId>org.apache.maven.plugins</groupId>-->
    <!--<artifactId>maven-surefire-plugin</artifactId>-->
    <!--</plugin>-->

    <!--<plugin>-->
    <!--<artifactId>maven-failsafe-plugin</artifactId>-->
    <!--</plugin>-->
    <!--</plugins>-->
    <!--</build>-->
</project>