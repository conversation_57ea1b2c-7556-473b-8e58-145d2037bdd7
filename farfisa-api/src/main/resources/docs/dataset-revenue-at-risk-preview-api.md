# Dataset Revenue At Risk Preview API

## Overview

The enhanced preview endpoint allows users to calculate RAR (Revenue At Risk) values without persisting changes to the database. This supports two main scenarios:

1. **Custom Data Preview** - Test specific RAR parameters
2. **Template Preview** - Preview the impact of workspace templates

## Endpoint

```
POST /api/datasets/{datasetId}/revenue-at-risks/{rarId}/preview
```

## Request Examples

### 1. Preview with Custom RAR Data

Test specific risk parameters to see their calculated impact:

```json
{
  "customRevenueAtRisk": {
    "customerSpendAvgAnnual": 100.0,
    "customerAdditionalCost": 10.0,
    "numberOfCustomers": 50,
    "totalYear": 1,
    "employeeSalary": 0.0,
    "employeeAdditionalCost": 0.0,
    "numberOfEmployees": 1,
    "scaleToTotalPeople": false,
    "valueAtRiskType": "CUSTOMER",
    "valueAtRiskWeight": "HIGH",
    "currency": "USD"
  }
}
```

### 2. Preview with Workspace Template

Preview the impact of applying a specific workspace template:

```json
{
  "previewTemplateId": 789
}
```

### 3. Fallback Preview (Empty Request)

Re-calculate existing configuration with current dataset content:

```json
{}
```

## Response Format

All preview requests return the same response structure:

```json
{
  "id": 1,
  "datasetId": 123,
  "name": "Configuration Name",
  "templateId": 789,
  "createdBy": 1,
  "revenueAtRisk": {
    "customerSpendAvgAnnual": 100.0,
    "customerAdditionalCost": 10.0,
    "numberOfCustomers": 50,
    "totalYear": 1,
    "employeeSalary": 0.0,
    "employeeAdditionalCost": 0.0,
    "numberOfEmployees": 1,
    "scaleToTotalPeople": false,
    "valueAtRiskType": "CUSTOMER",
    "valueAtRiskWeight": "HIGH",
    "currency": "USD",
    "valueAtRiskAmount": 2500.00,
    "selectedThemeIds": [1, 2, 3],
    "valueAtRiskAmountString": "2,500.00",
    "currencySymbol": "$",
    "totalAmount": 5000.0,
    "totalCustomerAmount": 5000.0,
    "totalEmployeeAmount": 0.0,
    "numberOfScalePeople": 50
  },
  "createdAt": "2025-08-14T16:38:07.037022",
  "updatedAt": null
}
```

## Use Cases

### 1. Parameter Tuning
Users can adjust risk weights, customer counts, or spending amounts to see immediate impact on calculated risk values.

### 2. Template Comparison
Users can preview different workspace templates to compare their effectiveness for a specific dataset.

### 3. Scenario Modeling
Users can model different business scenarios by adjusting parameters before committing to a configuration.

### 4. Validation
Users can verify calculations before saving configurations.

## Error Responses

### No Preview Data
```json
{
  "error": "No preview data provided. Please specify either customRevenueAtRisk or previewTemplateId."
}
```

### Template Not Found
```json
{
  "error": "Template not found or access denied"
}
```

### Access Denied
```json
{
  "error": "Access denied to dataset"
}
```
