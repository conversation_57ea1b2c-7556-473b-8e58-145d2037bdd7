package com.adoreboard.emotics.api.dataset.controller;

import com.adoreboard.emotics.api.authentication.AuthenticationUser;
import com.adoreboard.emotics.api.dataset.model.DatasetRevenueAtRiskPreviewRequest;
import com.adoreboard.emotics.api.dataset.model.DatasetRevenueAtRiskRequest;
import com.adoreboard.emotics.api.dataset.model.TemplateAssociationRequest;
import com.adoreboard.emotics.api.dataset.service.DatasetRevenueAtRiskService;
import com.adoreboard.emotics.common.model.User;
import com.adoreboard.emotics.common.model.revenueAtRisk.DatasetRevenueAtRisk;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(value = "/api/datasets")
public class DatasetRevenueAtRiskController {

    @Autowired
    private DatasetRevenueAtRiskService datasetRevenueAtRiskService;

    /**
     * <pre>
     * Preview RAR calculation with custom data or workspace template.
     *
     * This endpoint supports two preview scenarios:
     * 1. Preview with custom RAR parameters - User provides specific risk parameters to test
     * 2. Preview with workspace template - User selects a template to preview its impact
     *
     * The preview calculation is performed without persisting any changes to the database.
     * This allows users to experiment with different parameters or templates before committing.
     *
     * Request Body Examples:
     *
     * Custom Data Preview:
     * {
     *   "revenueAtRisk": {
     *     "customerSpendAvgAnnual": 100.0,
     *     "numberOfCustomers": 50,
     *     "valueAtRiskType": "CUSTOMER",
     *     "valueAtRiskWeight": "HIGH"
     *   }
     * }
     *
     * Template Preview:
     * {
     *   "templateId": 789
     * }
     *
     * @param user The authenticated user requesting the preview
     * @param datasetId The ID of the dataset to preview against
     * @param rarId The ID of the existing RAR configuration (optional, can be null for new previews)
     * @param request The preview request containing either custom data or template ID
     * @return The preview result with calculated values (not persisted)
     * </pre>
     */
    @ApiOperation(value = "Preview RAR calculation with custom data or template")
    @PostMapping(value = "/{datasetId}/revenue-at-risks/{rarId}/preview")
    @PreAuthorize("@datasetValidator.isAuthorised(#user, #datasetId)")
    public DatasetRevenueAtRisk previewVarInfo(
            @AuthenticationUser User user,
            @PathVariable int datasetId,
            @PathVariable Integer rarId,
            @RequestBody DatasetRevenueAtRiskPreviewRequest request) {

        return datasetRevenueAtRiskService.previewDatasetRevenueAtRisk(user, datasetId, rarId, request);
    }

    /**
     * Delete a dataset Revenue At Risk template
     */
    @DeleteMapping("/{datasetId}/revenue-at-risks/{rarId}")
    @PreAuthorize("@datasetValidator.isAuthorisedEditor(#user, #datasetId)")
    public ResponseEntity<Void> deleteDatasetTemplate(
            @AuthenticationUser User user,
            @PathVariable Integer datasetId,
            @PathVariable Integer rarId) {
        datasetRevenueAtRiskService.deleteDatasetRevenueAtRisk(user, rarId);
        return ResponseEntity.ok().build();
    }

    // ============================================================================
    // Specialized Update Operations
    // ============================================================================

    /**
     * @param user The authenticated user making the change
     * @param datasetId The ID of the dataset (for authorization)
     * @param rarId The ID of the RAR configuration to update
     * @param request Request body containing the new template ID
     * @return The updated configuration with new template association
     */
    @PutMapping("/{datasetId}/revenue-at-risks/{rarId}/apply-template")
    @PreAuthorize("@datasetValidator.isAuthorisedEditor(#user, #datasetId)")
    public ResponseEntity<DatasetRevenueAtRisk> applyTemplateForDatasetRar(
            @AuthenticationUser User user,
            @PathVariable Integer datasetId,
            @PathVariable Integer rarId,
            @RequestBody TemplateAssociationRequest request) {

        DatasetRevenueAtRisk updatedConfig = datasetRevenueAtRiskService.updateDatasetLinkedTemplate(user, datasetId, rarId, request.getTemplateId());
        return ResponseEntity.ok(updatedConfig);
    }

    /**
     * Update the revenue-at-risk data for a non-template-linked configuration.
     *
     * This endpoint is for updating RAR parameters and values for configurations
     * that are not linked to workspace templates. It allows full customization
     * of risk parameters independent of templates.
     *
     * Business Logic:
     * - Only works for configurations not linked to templates (templateId = null)
     * - Updates all provided RAR parameters and metadata
     * - Automatically recalculates risk amounts based on new parameters
     * - Maintains configuration independence from workspace templates
     *
     * Use Cases:
     * - Custom risk configurations that don't follow workspace templates
     * - Fine-tuned parameters specific to individual datasets
     * - Configurations that were unlinked from templates
     *
     * @param user The authenticated user making the change
     * @param datasetId The ID of the dataset (for authorization)
     * @param rarId The ID of the RAR configuration to update
     * @param datasetRevenueAtRisk The updated configuration data
     * @return The updated configuration with recalculated values
     */
    @PutMapping("/{datasetId}/revenue-at-risks/{rarId}")
    @PreAuthorize("@datasetValidator.isAuthorisedEditor(#user, #datasetId)")
    public ResponseEntity<DatasetRevenueAtRisk> updateRevenueAtRiskData(
            @AuthenticationUser User user,
            @PathVariable Integer datasetId,
            @PathVariable Integer rarId,
            @RequestBody DatasetRevenueAtRisk datasetRevenueAtRisk) {
        datasetRevenueAtRisk.setId(rarId);
        datasetRevenueAtRisk.setDatasetId(datasetId);
        DatasetRevenueAtRisk updatedConfig = datasetRevenueAtRiskService.updateDatasetRevenueAtRiskData(user, datasetRevenueAtRisk);
        return ResponseEntity.ok(updatedConfig);
    }


    @GetMapping("/{datasetId}/revenue-at-risk")
    public DatasetRevenueAtRisk getSelectedRevenueAtRisk(@AuthenticationUser User user, @PathVariable int datasetId, @RequestParam (required = false) Boolean isCreateNew, @RequestParam (required = false) Integer datasetRarId) {
        if(datasetRarId != null) {
            return  datasetRevenueAtRiskService.getDatasetRevenueAtRisk(user, datasetId, datasetRarId);
        }
        return datasetRevenueAtRiskService.getDatasetSelectedRevenueAtRisk(user, datasetId, isCreateNew);
    }

    @PutMapping("/{datasetId}/revenue-at-risk")
    public ResponseEntity<DatasetRevenueAtRisk> updateDatasetRevenueAtRisk(
            @AuthenticationUser User user,
            @PathVariable int datasetId,
            @RequestBody DatasetRevenueAtRiskRequest datasetRevenueAtRiskRequest) {
        return ResponseEntity.ok(datasetRevenueAtRiskService.updateDatasetRevenueAtRisk(user, datasetId, datasetRevenueAtRiskRequest));
    }

    @PostMapping("/{datasetId}/revenue-at-risk/preview")
    public DatasetRevenueAtRisk previewDatasetRevenueAtRisk(
            @AuthenticationUser User user,
            @PathVariable int datasetId,
            @RequestBody DatasetRevenueAtRiskPreviewRequest request) {
        Integer selectedRarId = datasetRevenueAtRiskService.getSelectedRevenueAtRiskIdForDataset(user, datasetId);
        return datasetRevenueAtRiskService.previewDatasetRevenueAtRisk(user, datasetId, selectedRarId, request);
    }

    @PutMapping("/{datasetId}/revenue-at-risk/apply-template")
    @PreAuthorize("@datasetValidator.isAuthorisedEditor(#user, #datasetId)")
    public ResponseEntity<DatasetRevenueAtRisk> applyTemplate(
            @AuthenticationUser User user,
            @PathVariable Integer datasetId,
            @RequestBody TemplateAssociationRequest request) {
        DatasetRevenueAtRisk updatedConfig = datasetRevenueAtRiskService.applyTemplateForDatasetSelectedRevenueAtRisk(user, datasetId, request.getTemplateId());
        return ResponseEntity.ok(updatedConfig);
    }
}
