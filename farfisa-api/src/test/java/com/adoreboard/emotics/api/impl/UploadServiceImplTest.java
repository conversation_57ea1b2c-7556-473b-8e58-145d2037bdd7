/*
 * Copyright 2021 Adoreboard Ltd. All rights reserved.
 * ADOREBOARD PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */

package com.adoreboard.emotics.api.impl;

import com.adoreboard.emotics.common.model.preprocessing.enums.PreProcessStatus;
import com.adoreboard.emotics.common.model.preprocessing.model.FileValidatorAcknowledgement;
import com.adoreboard.emotics.common.model.preprocessing.model.PreProcessingAcknowledgement;
import com.adoreboard.emotics.api.preprocessing.service.PreProcessingService;
import com.adoreboard.emotics.api.preprocessing.utils.PreProcessingUtils;
import com.adoreboard.emotics.api.stopword.mapper.GlobalStopwordListMapper;
import com.adoreboard.emotics.api.upload.service.BatchService;
import com.adoreboard.emotics.api.upload.service.impl.UploadServiceImpl;
import com.adoreboard.emotics.api.user.service.UserService;
import com.adoreboard.emotics.common.api.IntercomApi;
import com.adoreboard.emotics.common.enums.DatasetStatus;
import com.adoreboard.emotics.common.enums.StopWordStatus;
import com.adoreboard.emotics.common.mapper.DatasetDownloadDetailsMapper;
import com.adoreboard.emotics.common.mapper.DatasetMapper;
import com.adoreboard.emotics.common.mapper.StopWordMapper;
import com.adoreboard.emotics.common.model.*;
import com.adoreboard.emotics.common.service.ApplyDatasetRevenueAtRiskService;
import com.adoreboard.emotics.common.service.ProgressUpdateService;
import org.apache.commons.io.FileUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.batch.core.JobParameter;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.BDDMockito.given;
import static org.mockito.Matchers.*;
import static org.mockito.Mockito.verify;

/**
 * Test class of UploadServiceImpl
 * <p>
 * Created by Max N on 09/09/2021.
 */
@RunWith(MockitoJUnitRunner.class)
public class UploadServiceImplTest {

    @InjectMocks
    private UploadServiceImpl uploadService;

    @Mock private BatchService batchService;
    @Mock private DatasetDownloadDetailsMapper datasetDownloadDetailsMapper;
    @Mock private DatasetMapper datasetMapper;
    @Mock private GlobalStopwordListMapper globalStopwordListMapper;
    @Mock private IntercomApi intercomApi;
    @Mock private PreProcessingService preProcessingService;
    @Mock private ProgressUpdateService progressUpdateService;
    @Mock private StopWordMapper stopWordMapper;
    @Mock private UserService userService;
    @Mock private ApplyDatasetRevenueAtRiskService applyDatasetRevenueAtRiskService;

    private User user;
    private UserTier userTier;
    private UsageRecord usageRecord;
    private List<PreProcessingAcknowledgement> acknowledgements;

    @Before
    public void setup() throws Exception {
        userTier = new UserTier();
        userTier.setName("internal");

        user = new User();
        userTier = new UserTier();
        usageRecord = new UsageRecord();

        userTier.setName("internal");

        user.setId(1);
        user.setEmail("email");
        user.setUserTier(userTier);

        usageRecord.setMaxUsage(-1L);

        acknowledgements = new ArrayList<>();
        final PreProcessingAcknowledgement acknowledgement = new PreProcessingAcknowledgement();
        acknowledgement.setStatus(PreProcessStatus.FINISHED);
        acknowledgement.setCommentColumns(Arrays.asList(0));
        acknowledgement.setMetadataHeaders(new String[0]);
        acknowledgement.setStopWords(Collections.singletonList("StopWords"));
        acknowledgement.setLabel("Label");
        acknowledgement.setWorkspaceId(99);
        final FileValidatorAcknowledgement fileAcknowledgement = new FileValidatorAcknowledgement();
        fileAcknowledgement.setDocCount(new int[1]);
        fileAcknowledgement.setAllowedCharacterCount(1_000_000);
        acknowledgement.setFileAcknowledgement(fileAcknowledgement);
        acknowledgements.add(acknowledgement);

        given(userService.retrieveUsageRecord(user)).willReturn(usageRecord);
    }

    @Test
    public void shouldUploadAcknowledgementsAndInsertStopWords() {
        // Given
        ArgumentCaptor<List<StopWord>> stopWordsCaptor = ArgumentCaptor.forClass((Class) List.class);

        given(userService.retrieveUsageRecord(user)).willReturn(usageRecord);
        given(stopWordMapper.selectByWorkspaceId(99)).willReturn(Collections.singletonList(new StopWord("Workspace", StopWordStatus.inserted, 99)));
        // When
        uploadService.upload(user, acknowledgements, false);

        // Then
        verify(stopWordMapper).insertList(stopWordsCaptor.capture());
        assertEquals("Workspace", stopWordsCaptor.getValue().get(0).getStopWord());
        assertEquals("StopWords", stopWordsCaptor.getValue().get(1).getStopWord());


    }

    @Test
    public void shouldUploadAndRemoveAcknowledgements() {
        // Given
        given(batchService.processFileForAnalysis(
                user,
                0,
                PreProcessingUtils.getTmpUploadFile(acknowledgements.get(0).getId()).getAbsolutePath(),
                acknowledgements.get(0).getFileAcknowledgement().getExtension(),
                acknowledgements.get(0).getStopWords(),
                acknowledgements.get(0).getInputLanguage(),
                acknowledgements.get(0).getCommentColumns(),
                -1,
                null,
                false,
                new HashMap<String, JobParameter>() {{
                    put("metadata", new JobParameter(""));
                    put("metadataTypes", new JobParameter(""));
                    put("commentHeaders", new JobParameter(""));
                    put("timestampFormats", new JobParameter(""));
                }},
                acknowledgements.get(0).getCustomThemeList(),
                acknowledgements.get(0).isExcludeAutoTopics(),
                acknowledgements.get(0).isExcludeColumnHeaders()
        )).willReturn(true);

        given(userService.retrieveUsageRecord(user)).willReturn(usageRecord);

        // When
        uploadService.upload(user, acknowledgements, false);

        // Then
        verify(batchService).processFileForAnalysis(
                user,
                0,
                PreProcessingUtils.getTmpUploadFile(acknowledgements.get(0).getId()).getAbsolutePath(),
                acknowledgements.get(0).getFileAcknowledgement().getExtension(),
                acknowledgements.get(0).getStopWords(),
                acknowledgements.get(0).getInputLanguage(),
                acknowledgements.get(0).getCommentColumns(),
                -1,
                null,
                false,
                new HashMap<String, JobParameter>() {{
                    put("metadata", new JobParameter(""));
                    put("metadataTypes", new JobParameter(""));
                    put("commentHeaders", new JobParameter(""));
                    put("timestampFormats", new JobParameter(""));
                }},
                acknowledgements.get(0).getCustomThemeList(),
                acknowledgements.get(0).isExcludeAutoTopics(),
                acknowledgements.get(0).isExcludeColumnHeaders()
        );
        verify(preProcessingService).removeAcknowledgement(eq(user), eq(null), eq(false));
    }

    @Test
    public void shouldUploadAcknowledgementsAndRemoveTagFromContact() {
        // Given
        given(intercomApi.searchContactsByEmail(user.getEmail())).willReturn("contactId");

        given(userService.retrieveUsageRecord(user)).willReturn(usageRecord);

        // When
        uploadService.upload(user, acknowledgements, false);

        // Then
        verify(intercomApi).searchContactsByEmail(eq("email"));
        verify(intercomApi).removeTagFromContact(eq("contactId"), eq(4705778));
    }

    @Test
    public void shouldUploadAcknowledgements() {
        // Given
        given(batchService.processFileForAnalysis(
                user,
                0,
                PreProcessingUtils.getTmpUploadFile(acknowledgements.get(0).getId()).getAbsolutePath(),
                acknowledgements.get(0).getFileAcknowledgement().getExtension(),
                acknowledgements.get(0).getStopWords(),
                acknowledgements.get(0).getInputLanguage(),
                acknowledgements.get(0).getCommentColumns(),
                -1,
                null,
                false,
                new HashMap<String, JobParameter>() {{
                    put("metadata", new JobParameter(""));
                    put("metadataTypes", new JobParameter(""));
                    put("commentHeaders", new JobParameter(""));
                    put("timestampFormats", new JobParameter(""));
                }},
                acknowledgements.get(0).getCustomThemeList(),
                acknowledgements.get(0).isExcludeAutoTopics(),
                acknowledgements.get(0).isExcludeColumnHeaders()
        )).willReturn(true);

        given(userService.retrieveUsageRecord(user)).willReturn(usageRecord);

        // When
        final List<Dataset> datasets = uploadService.upload(user, acknowledgements, false);

        // Then
        assertEquals(1, datasets.size());
        assertEquals(1, datasets.get(0).getUserId());
        assertEquals("Label", datasets.get(0).getLabel());
        assertEquals(DatasetStatus.uploading, datasets.get(0).getStatus());
    }

    @Test
    public void shouldUploadFileAndAcknowledgements() throws IOException {

        // Setup
        File inputFile = new File("./target/test.csv");
        FileUtils.writeStringToFile(inputFile, "I love the food.");
        MultipartFile multipartFile = new MockMultipartFile("test.csv",
                new FileInputStream(inputFile));

        // Given
        given(batchService.processFileForAnalysis(
                user,
                0,
                PreProcessingUtils.getTmpUploadFile(acknowledgements.get(0).getId()).getAbsolutePath(),
                acknowledgements.get(0).getFileAcknowledgement().getExtension(),
                acknowledgements.get(0).getStopWords(),
                acknowledgements.get(0).getInputLanguage(),
                acknowledgements.get(0).getCommentColumns(),
                -1,
                null,
                false,
                new HashMap<String, JobParameter>() {{
                    put("metadata", new JobParameter(""));
                    put("metadataTypes", new JobParameter(""));
                    put("commentHeaders", new JobParameter(""));
                    put("timestampFormats", new JobParameter(""));
                }},
                acknowledgements.get(0).getCustomThemeList(),
                acknowledgements.get(0).isExcludeAutoTopics(),
                acknowledgements.get(0).isExcludeColumnHeaders()
        )).willReturn(true);

        given(userService.retrieveUsageRecord(user)).willReturn(usageRecord);

        given(preProcessingService.queueForPreProcessing(any(User.class), any(MultipartFile.class), any(String.class), anyBoolean(), anyListOf(Integer.class), anyInt())).willReturn(acknowledgements.get(0));

        // When
        final Dataset dataset = uploadService.upload(user, multipartFile, "ABC",
                acknowledgements.get(0).getWorkspaceId(),
                acknowledgements.get(0).getCustomThemeList(),
                acknowledgements.get(0).getCommentColumns(),
                acknowledgements.get(0).getCommentColumnsHeaders(),
                acknowledgements.get(0).getStopWords(),
                acknowledgements.get(0).getSavedActionListIds(),
                acknowledgements.get(0).getMetadataColumns(),
                acknowledgements.get(0).getMetadataHeaders(),
                acknowledgements.get(0).getMetadataTypes(),
                acknowledgements.get(0).isExcludeColumnHeaders(),
                acknowledgements.get(0).isExcludeAutoTopics()
        );

        // Then
        assertEquals(true, dataset != null);
        assertEquals(1, dataset.getUserId());
        assertEquals("ABC", dataset.getLabel());
        assertEquals(DatasetStatus.uploading, dataset.getStatus());
    }
}
