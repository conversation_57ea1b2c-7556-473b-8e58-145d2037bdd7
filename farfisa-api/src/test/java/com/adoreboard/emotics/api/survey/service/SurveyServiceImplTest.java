package com.adoreboard.emotics.api.survey.service;

import com.adoreboard.emotics.api.dataset.service.DatasetService;
import com.adoreboard.emotics.api.dataset.service.DatasetTagService;
import com.adoreboard.emotics.api.survey.model.HeartBeats;
import com.adoreboard.emotics.api.survey.model.QuestionResponse;
import com.adoreboard.emotics.api.survey.model.RespondentInfoResponse;
import com.adoreboard.emotics.api.survey.model.SurveyResponse;
import com.adoreboard.emotics.api.workspace.service.WorkspaceValidatorService;
import com.adoreboard.emotics.common.enums.DatasetFeature;
import com.adoreboard.emotics.common.exception.EmoticsException;
import com.adoreboard.emotics.common.mapper.DatasetMapper;
import com.adoreboard.emotics.common.mapper.SurveyMapper;
import com.adoreboard.emotics.common.mapper.impl.UserContentBatchMapper;
import com.adoreboard.emotics.common.mapper.param.SurveyParam;
import com.adoreboard.emotics.common.model.Dataset;
import com.adoreboard.emotics.common.model.User;
import com.adoreboard.emotics.common.model.UserContent;
import com.adoreboard.emotics.common.model.metadata.MetadataFilterType;
import com.adoreboard.emotics.common.model.survey.*;
import com.adoreboard.emotics.common.model.survey.enums.RespondentInfoType;
import com.adoreboard.emotics.common.service.ApplyDatasetRevenueAtRiskService;
import com.adoreboard.emotics.common.service.ProgressUpdateService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.Assert.*;
import static org.mockito.BDDMockito.given;
import static org.mockito.Matchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class SurveyServiceImplTest {

    @Mock private SurveyMapper surveyMapper;
    @Mock private DatasetMapper datasetMapper;
    @Mock private DatasetTagService datasetTagService;
    @Mock private UserContentBatchMapper userContentBatchMapper;
    @Mock private DatasetService datasetService;
    @Mock private ProgressUpdateService progressUpdateService;
    @Mock private WorkspaceValidatorService workspaceValidatorService;
    @Mock private ApplyDatasetRevenueAtRiskService applyDatasetRevenueAtRiskService;

    @InjectMocks
    private SurveyServiceImpl surveyService;

    private static final int USER_ID = 1;
    private static final int WORKSPACE_ID = 2;
    private static final int SURVEY_ID = 100;
    private static final Survey DUMMY_SURVEY = buildDummySurvey("dummy survey",
            SurveyStatus.DRAFT, Arrays.asList(), null);

    private static final List<SurveyQuestion> DUMMY_QUESTIONS = Arrays.asList(
            SurveyQuestion.builder()
                    .questionNumber(1)
                    .type(QuestionType.OPEN_TEXT)
                    .description("Question 1")
                    .required(true)
                    .build(),
            SurveyQuestion.builder()
                    .questionNumber(2)
                    .type(QuestionType.OPEN_TEXT)
                    .description("Question 2")
                    .required(true)
                    .build(),
            SurveyQuestion.builder()
                    .questionNumber(3)
                    .type(QuestionType.METRIC_RANGE)
                    .description("Question 3")
                    .required(true)
                    .build(),
            SurveyQuestion.builder()
                    .questionNumber(4)
                    .type(QuestionType.METRIC_RANGE)
                    .description("Question 4")
                    .required(false)
                    .build()
    );

    private User user;

    @Before
    public void setup() throws Exception {
        user = new User();
        user.setId(USER_ID);
    }

    @Test
    public void shouldQuerySurveys() throws Exception {
        // Given
        ArgumentCaptor<SurveyParam> paramArgumentCaptor = ArgumentCaptor.forClass(SurveyParam.class);
        String term = "Heart Beat Test Survey";
        given(surveyMapper.count(paramArgumentCaptor.capture())).willReturn(10);
        given(surveyMapper.query(paramArgumentCaptor.capture())).willReturn(Arrays.asList(new SurveyWithUser()));

        // When
        HeartBeats surveys = surveyService.querySurveys(user, WORKSPACE_ID, null, 5, 1, 0, term, "asc", "id");

        // Then
        verify(workspaceValidatorService).validateEditorPermission(USER_ID, WORKSPACE_ID);
        assertEquals(1, surveys.getHeartbeats().size());

        List<SurveyParam> captured = paramArgumentCaptor.getAllValues();
        assertEquals(0, captured.get(0).getOffset().intValue());
        // FIXME: Vu: Chau has forced offset to be aligned with the offset rather than calculation
        //        assertEquals(5, captured.get(1).getOffset().intValue());
    }

    @Test
    public void shouldQuerySurveyByUuid() throws Exception {
        // Given
        String uuid = "uuid-v4-123";
        given(surveyMapper.selectSurveyByUuid(uuid)).willReturn(Optional.of(DUMMY_SURVEY));
        // When
        surveyService.querySurveyByUuid(uuid);
        // Then
        verify(workspaceValidatorService, times(0)).validateEditorPermission(any(Integer.class), any(Integer.class));
    }

    @Test
    public void shouldQueryById() throws Exception {
        // Given
        given(surveyMapper.selectSurveyById(SURVEY_ID)).willReturn(Optional.of(DUMMY_SURVEY));
        // When
        surveyService.querySurveyById(user, WORKSPACE_ID, SURVEY_ID);
        // Then
        verify(workspaceValidatorService).validateEditorPermission(USER_ID, WORKSPACE_ID);
    }

    @Test
    public void shouldQueryByDatasetId() throws Exception {
        // Given
        int datasetId = 10;
        given(surveyMapper.selectSurveyByDatasetId(datasetId)).willReturn(Optional.of(DUMMY_SURVEY));
        // When
        surveyService.querySurveyByDatasetId(user, WORKSPACE_ID, datasetId);
        // Then
        verify(workspaceValidatorService).validateEditorPermission(USER_ID, WORKSPACE_ID);
    }

    @Test
    public void shouldQueryByDatasetIds() {
        // Given
        int datasetId = 10;
        given(surveyMapper.selectSurveyByDatasetId(datasetId)).willReturn(Optional.of(DUMMY_SURVEY));
        // When
        HeartBeats surveys = surveyService.querySurveys(user, WORKSPACE_ID, Arrays.asList(datasetId), 5, 1, 0, "", "asc", "id");
        // Then
        verify(workspaceValidatorService).validateEditorPermission(USER_ID, WORKSPACE_ID);
    }

    @Test
    public void shouldCreateDraftSurvey() throws Exception {
        // Given
        Survey surveyBody = Survey.builder()
                .label("my survey")
                .questions(Collections.singletonList(new SurveyQuestion()))
                .description("nothing to see here")
                .status(SurveyStatus.DRAFT)
                .build();

        // When
        Survey createdSurvey = surveyService.createSurvey(user, WORKSPACE_ID, surveyBody);

        // Then
        verify(workspaceValidatorService).validateEditorPermission(USER_ID, WORKSPACE_ID);
        assertEquals(USER_ID, createdSurvey.getUserId().intValue());
        assertFalse("uuid must be created", createdSurvey.getSurveyUuid().isEmpty());
        assertNotNull(createdSurvey.getCreatedAt());
        assertNotNull(createdSurvey.getUpdatedAt());
        assertEquals(WORKSPACE_ID, createdSurvey.getWorkspaceId().intValue());
        verify(datasetMapper, times(0)).insert(any(Dataset.class));
    }

    @Test
    public void shouldCreateLiveSurvey() throws Exception {
        // Given
        SurveyQuestion question = new SurveyQuestion();
        question.setDescription("question 1");
        question.setType(QuestionType.METRIC_RANGE);

        ArgumentCaptor<Dataset> captor = ArgumentCaptor.forClass(Dataset.class);
        Survey surveyBody = Survey.builder()
                .label("my survey")
                .questions(Collections.singletonList(question))
                .description("nothing to see here")
                .status(SurveyStatus.LIVE)
                .build();

        // When
        Survey createdSurvey = surveyService.createSurvey(user, WORKSPACE_ID, surveyBody);

        // Then
        verify(workspaceValidatorService).validateEditorPermission(USER_ID, WORKSPACE_ID);
        assertEquals(USER_ID, createdSurvey.getUserId().intValue());
        assertTrue("uuid must be created", !createdSurvey.getSurveyUuid().isEmpty());
        assertNotNull(createdSurvey.getCreatedAt());
        assertNotNull(createdSurvey.getUpdatedAt());
        assertEquals(WORKSPACE_ID, createdSurvey.getWorkspaceId().intValue());

        verify(surveyMapper).createSurvey(any());

        verify(datasetMapper).insert(captor.capture());
        Dataset capturedDataset = captor.getValue();
        assertEquals(USER_ID, capturedDataset.getUserId());
        assertEquals("my survey", capturedDataset.getLabel());
        assertEquals(WORKSPACE_ID, capturedDataset.getWorkspaceId().intValue());
        assertEquals(USER_ID, capturedDataset.getUserId());
        assertEquals(1, capturedDataset.getFeatures().size());
        assertEquals(DatasetFeature.Structured, capturedDataset.getFeatures().get(0));
        assertEquals(question.getDescription(), capturedDataset.getMetadataHeaders().get(2));
        assertEquals(MetadataFilterType.SCORE.name(), capturedDataset.getMetadataTypes().get(2));
        assertEquals(0, capturedDataset.getMetadataColumns().get(0).intValue());
    }

    @Test
    public void shouldChangeSurveyStatusToClose() throws Exception {
        // Given
        ArgumentCaptor<Survey> surveyCaptor = ArgumentCaptor.forClass(Survey.class);
        given(surveyMapper.selectSurveyById(SURVEY_ID)).willReturn(Optional.of(DUMMY_SURVEY));
        // When
        surveyService.changeSurveyStatus(user, WORKSPACE_ID, SURVEY_ID, SurveyStatus.CLOSED);
        // Then
        verify(workspaceValidatorService).validateEditorPermission(USER_ID, WORKSPACE_ID);
        verify(datasetMapper, times(0)).insert(any(Dataset.class));

        verify(surveyMapper).updateSurvey(surveyCaptor.capture());
        Survey capturedSurvey = surveyCaptor.getValue();
        assertEquals(SurveyStatus.CLOSED, capturedSurvey.getStatus());
    }

    @Test
    public void shouldChangeSurveyStatusToLive() throws Exception {
        // Given
        ArgumentCaptor<Survey> surveyCaptor = ArgumentCaptor.forClass(Survey.class);
        given(surveyMapper.selectSurveyById(SURVEY_ID)).willReturn(Optional.of(DUMMY_SURVEY));

        SurveyQuestion question = new SurveyQuestion();
        question.setDescription("question 1");
        question.setType(QuestionType.METRIC_RANGE);

        ArgumentCaptor<Dataset> captor = ArgumentCaptor.forClass(Dataset.class);

        // When
        Survey updatedSurvey = surveyService.changeSurveyStatus(user, WORKSPACE_ID, SURVEY_ID, SurveyStatus.LIVE);

        // Then
        verify(workspaceValidatorService).validateEditorPermission(USER_ID, WORKSPACE_ID);

        verify(datasetMapper).insert(captor.capture());
        Dataset capturedDataset = captor.getValue();
        assertEquals(USER_ID, capturedDataset.getUserId());
        assertEquals("dummy survey", capturedDataset.getLabel());
        assertEquals(WORKSPACE_ID, capturedDataset.getWorkspaceId().intValue());
        assertEquals(USER_ID, capturedDataset.getUserId());
        assertEquals(1, capturedDataset.getFeatures().size());

        verify(surveyMapper).updateSurvey(surveyCaptor.capture());
        Survey capturedSurvey = surveyCaptor.getValue();
        assertEquals(SurveyStatus.LIVE, capturedSurvey.getStatus());
        assertEquals(SurveyStatus.LIVE, updatedSurvey.getStatus());
    }

    @Test(expected = EmoticsException.class)
    public void shouldNotChangeSurveyStatusWhenNotExists() throws Exception {
        given(surveyMapper.selectSurveyById(SURVEY_ID)).willReturn(Optional.empty());

        // When
        surveyService.changeSurveyStatus(user, WORKSPACE_ID, SURVEY_ID, SurveyStatus.LIVE);

        // Then
    }

    @Test
    public void shouldUpdateSurveyQuestionsIfPublished() throws Exception {
        // Given
        ArgumentCaptor<Survey> surveyCaptor = ArgumentCaptor.forClass(Survey.class);
        SurveyQuestion question = new SurveyQuestion();
        question.setDescription("question 1");
        question.setType(QuestionType.METRIC_RANGE);

        Survey testSurvey = SurveyWithUser.builder()
                .id(SURVEY_ID)
                .userId(USER_ID)
                .label("new dummy survey")
                .description("new description")
                .workspaceId(WORKSPACE_ID)
                .status(SurveyStatus.DRAFT)
                .questions(Arrays.asList(question))
                .build();

        given(surveyMapper.selectSurveyById(SURVEY_ID)).willReturn(Optional.of(DUMMY_SURVEY));

        // When
        surveyService.updateSurveyQuestions(user, WORKSPACE_ID, testSurvey);

        // Then
        verify(workspaceValidatorService).validateEditorPermission(USER_ID, WORKSPACE_ID);
        verify(surveyMapper).updateSurvey(surveyCaptor.capture());
        Survey capturedSurvey = surveyCaptor.getValue();
        assertEquals(question, capturedSurvey.getQuestions().get(0));
        assertEquals("new description", capturedSurvey.getDescription());
        assertEquals("new dummy survey", capturedSurvey.getLabel());
    }

    @Test (expected = EmoticsException.class)
    public void shouldNotUpdateSurveyQuestionsWhenResponded() throws Exception {
        // Given
        SurveyQuestion question = new SurveyQuestion();
        question.setDescription("question 1");
        question.setType(QuestionType.METRIC_RANGE);

        Survey dbSurvey = SurveyWithUser.builder()
                .id(SURVEY_ID)
                .userId(USER_ID)
                .label("dummy survey")
                .description("description")
                .workspaceId(WORKSPACE_ID)
                .status(SurveyStatus.LIVE)
                .questions(Arrays.asList())
                .totalResponses(10)
                .build();

        Survey testSurvey = SurveyWithUser.builder()
                .id(SURVEY_ID)
                .userId(USER_ID)
                .label("new dummy survey")
                .description("new description")
                .workspaceId(WORKSPACE_ID)
                .status(SurveyStatus.LIVE)
                .questions(Arrays.asList(question))
                .build();

        given(surveyMapper.selectSurveyById(SURVEY_ID)).willReturn(Optional.of(dbSurvey));

        // When
        surveyService.updateSurveyQuestions(user, WORKSPACE_ID, testSurvey);

        // Then
    }

    @Test
    public void shouldDeleteSurvey() throws Exception {
        // Given
        int[] surveyIds = new int[] { 1, 2, 3 };
        given(surveyMapper.selectSurveyById(1)).willReturn(Optional.of(DUMMY_SURVEY));
        given(surveyMapper.selectSurveyById(2)).willReturn(Optional.of(DUMMY_SURVEY));
        given(surveyMapper.selectSurveyById(3)).willReturn(Optional.of(DUMMY_SURVEY));

        // When
        surveyService.deleteSurveys(user, WORKSPACE_ID, surveyIds);

        // Then
        verify(workspaceValidatorService).validateEditorPermission(USER_ID, WORKSPACE_ID);
        verify(surveyMapper).deleteSurveyByIds(surveyIds);
    }

    @Test
    public void shouldNotDeleteSurveyWhenNotExists() throws Exception {
        // Given
        int[] surveyIds = new int[] { 1, 2, 3 };
        given(surveyMapper.selectSurveyById(1)).willReturn(Optional.of(DUMMY_SURVEY));
        given(surveyMapper.selectSurveyById(2)).willReturn(Optional.of(DUMMY_SURVEY));
        given(surveyMapper.selectSurveyById(3)).willReturn(Optional.empty());

        // When
        try {
            surveyService.deleteSurveys(user, WORKSPACE_ID, surveyIds);
        } catch (EmoticsException e) {
            assertEquals("No survey associated with id 3", e.getMessage());
        }
        // Then
        verify(workspaceValidatorService).validateEditorPermission(USER_ID, WORKSPACE_ID);
        verify(surveyMapper, times(0)).deleteSurveyByIds(surveyIds);
    }

    @Test
    public void shouldNotDeleteSurveyWhenEmptySurveyIds() throws Exception {
        // Given

        // When
        try {
            surveyService.deleteSurveys(user, WORKSPACE_ID, new int[]{});
        } catch (EmoticsException e) {
            assertEquals("SurveyIds param must be specified", e.getMessage());
        }
        // Then
        verify(workspaceValidatorService).validateEditorPermission(USER_ID, WORKSPACE_ID);
        verify(surveyMapper, times(0)).deleteSurveyByIds(any());
    }

    @Test
    public void shouldPutResponseToSurvey() throws Exception {
        // Given
        ArgumentCaptor<List<UserContent>> listCaptor
                = ArgumentCaptor.forClass((Class) List.class);

        String uuid = "123-456-789";
        Survey survey = buildDummySurvey("my survey",
                SurveyStatus.LIVE,
                DUMMY_QUESTIONS,
                null);
        survey.setDatasetId(200);
        given(surveyMapper.selectSurveyByUuid(uuid)).willReturn(Optional.of(survey));
        given(datasetMapper.selectById(200)).willReturn(buildDummyDataset(200, Arrays.asList("Question", "Response Date") ));

        QuestionResponse q1 = QuestionResponse.builder()
                .type(QuestionType.OPEN_TEXT)
                .questionNumber(1)
                .responseValue("My response1 is empty")
                .build();

        QuestionResponse q2 = QuestionResponse.builder()
                .type(QuestionType.OPEN_TEXT)
                .questionNumber(2)
                .responseValue("My response2 is empty")
                .build();

        QuestionResponse q3 = QuestionResponse.builder()
                .type(QuestionType.METRIC_RANGE)
                .questionNumber(3)
                .responseValue(5)
                .build();

        QuestionResponse q4 = QuestionResponse.builder()
                .type(QuestionType.METRIC_RANGE)
                .questionNumber(4)
                .responseValue(2)
                .build();

        RespondentInfoResponse info1 = RespondentInfoResponse.builder()
                .key("Email")
                .value("<EMAIL>")
                .build();

        RespondentInfoResponse info2 = RespondentInfoResponse.builder()
                .key("Phone")
                .value("+44 123 4567")
                .build();

        SurveyResponse surveyResponse = SurveyResponse.builder()
                .questionResponses(Arrays.asList(q1, q2, q3, q4))
                .respondentInfoResponses(Arrays.asList(info1, info2))
                .build();

        // When
        surveyService.putResponsesToSurvey(uuid, surveyResponse);

        // Then
        verify(userContentBatchMapper, timeout(200)).insertList(listCaptor.capture(), eq(200));
        List<UserContent> contents = listCaptor.getValue();
        assertEquals(2, contents.size());
        assertEquals("My response1 is empty", contents.get(0).getContent());
        assertEquals(USER_ID, contents.get(0).getUserId().intValue());
        assertNotNull(contents.get(0).getTimestamp());
        assertNotNull(contents.get(0).getContentTimestamp());
        assertEquals(4, (contents.get(0).getMetadata().length));
        assertEquals(survey.getQuestions().get(0).getDescription(), contents.get(0).getMetadata()[0]);
        assertEquals(5, ((Integer) contents.get(0).getMetadata()[2]).intValue());
        assertEquals(2, ((Integer) contents.get(0).getMetadata()[3]).intValue());

        assertEquals("My response2 is empty", contents.get(1).getContent());
        assertEquals(USER_ID, contents.get(1).getUserId().intValue());
        assertNotNull(contents.get(1).getTimestamp());
        assertNotNull(contents.get(1).getContentTimestamp());
        assertEquals(4, (contents.get(1).getMetadata().length));
        assertEquals(survey.getQuestions().get(1).getDescription(), contents.get(1).getMetadata()[0]);
        assertEquals(5, ((Integer) contents.get(1).getMetadata()[2]).intValue());
        assertEquals(2, ((Integer) contents.get(1).getMetadata()[3]).intValue());
    }

    @Test
    public void shouldPutResponseToSurveyWithRespondentInfo() throws Exception {
        // Given
        ArgumentCaptor<List<UserContent>> listCaptor
                = ArgumentCaptor.forClass((Class) List.class);

        String uuid = "123-456-789";
        Survey survey = buildDummySurvey("my survey",
                SurveyStatus.LIVE,
                DUMMY_QUESTIONS,
                Configurations.builder()
                        .respondentInfo(RespondentInfo.builder()
                                .displayed(true)
                                .properties(Arrays.asList(InfoProperty.builder()
                                                .required(true)
                                                .key("Email")
                                                .description("Email")
                                                .type(RespondentInfoType.EMAIL)
                                                .build(),
                                        InfoProperty.builder()
                                                .required(false)
                                                .key("Phone")
                                                .description("Phone")
                                                .type(RespondentInfoType.PHONE)
                                                .build()))
                                .build())
                        .build());
        survey.setDatasetId(200);

        given(surveyMapper.selectSurveyByUuid(uuid)).willReturn(Optional.of(survey));
        given(datasetMapper.selectById(anyInt())).willReturn(buildDummyDataset(200, Arrays.asList("Question")));

        QuestionResponse q1 = QuestionResponse.builder()
                .type(QuestionType.OPEN_TEXT)
                .questionNumber(1)
                .responseValue("My response1 is empty")
                .build();

        QuestionResponse q2 = QuestionResponse.builder()
                .type(QuestionType.OPEN_TEXT)
                .questionNumber(2)
                .responseValue("My response2 is empty")
                .build();

        QuestionResponse q3 = QuestionResponse.builder()
                .type(QuestionType.METRIC_RANGE)
                .questionNumber(3)
                .responseValue(5)
                .build();

        QuestionResponse q4 = QuestionResponse.builder()
                .type(QuestionType.METRIC_RANGE)
                .questionNumber(4)
                .responseValue(2)
                .build();

        RespondentInfoResponse info1 = RespondentInfoResponse.builder()
                .key("Email")
                .value("<EMAIL>")
                .build();

        RespondentInfoResponse info2 = RespondentInfoResponse.builder()
                .key("Phone")
                .value("+44 123 4567")
                .build();

        // NOTE: question & info order in list aren't important as they should be rearranged
        SurveyResponse surveyResponse = SurveyResponse.builder()
                .questionResponses(Arrays.asList(q1, q3, q4, q2))
                .respondentInfoResponses(Arrays.asList(info2, info1))
                .build();

        // When
        surveyService.putResponsesToSurvey(uuid, surveyResponse);

        // Then
        verify(userContentBatchMapper, timeout(200)).insertList(listCaptor.capture(), eq(200));
        List<UserContent> contents = listCaptor.getValue();
        assertEquals(2, contents.size());
        assertEquals("My response1 is empty", contents.get(0).getContent());
        assertEquals(USER_ID, contents.get(0).getUserId().intValue());
        assertNotNull(contents.get(0).getTimestamp());
        assertNotNull(contents.get(0).getContentTimestamp());
        assertEquals(5, (contents.get(0).getMetadata().length));
        assertEquals(survey.getQuestions().get(0).getDescription(), contents.get(0).getMetadata()[0]);
        assertEquals(5, ((Integer) contents.get(0).getMetadata()[1]).intValue());
        assertEquals(2, ((Integer) contents.get(0).getMetadata()[2]).intValue());
        assertEquals("<EMAIL>", contents.get(0).getMetadata()[3]);
        assertEquals("+44 123 4567", contents.get(0).getMetadata()[4]);

        assertEquals("My response2 is empty", contents.get(1).getContent());
        assertEquals(USER_ID, contents.get(1).getUserId().intValue());
        assertNotNull(contents.get(1).getTimestamp());
        assertNotNull(contents.get(1).getContentTimestamp());
        assertEquals(5, (contents.get(1).getMetadata().length));
        assertEquals(survey.getQuestions().get(1).getDescription(), contents.get(1).getMetadata()[0]);
        assertEquals(5, ((Integer) contents.get(1).getMetadata()[1]).intValue());
        assertEquals(2, ((Integer) contents.get(1).getMetadata()[2]).intValue());
        assertEquals("<EMAIL>", contents.get(1).getMetadata()[3]);
        assertEquals("+44 123 4567", contents.get(1).getMetadata()[4]);
    }

    @Test
    public void shouldPutResponseToSurveyWithRespondentInfoAutoFillNonRequired() throws Exception {
        // Given
        ArgumentCaptor<List<UserContent>> listCaptor
                = ArgumentCaptor.forClass((Class) List.class);

        String uuid = "123-456-789";
        Survey survey = buildDummySurvey("my survey",
                SurveyStatus.LIVE,
                DUMMY_QUESTIONS,
                Configurations.builder()
                        .respondentInfo(RespondentInfo.builder()
                                .displayed(true)
                                .properties(Arrays.asList(InfoProperty.builder()
                                                .required(true)
                                                .key("Email")
                                                .description("Email")
                                                .type(RespondentInfoType.EMAIL)
                                                .build(),
                                        InfoProperty.builder()
                                                .required(false)
                                                .key("Phone")
                                                .description("Phone")
                                                .type(RespondentInfoType.PHONE)
                                                .build()))
                                .build())
                        .build());
        survey.setDatasetId(200);

        given(surveyMapper.selectSurveyByUuid(uuid)).willReturn(Optional.of(survey));
        given(datasetMapper.selectById(200)).willReturn(buildDummyDataset(200, Arrays.asList("Question")));

        QuestionResponse q1 = QuestionResponse.builder()
                .type(QuestionType.OPEN_TEXT)
                .questionNumber(1)
                .responseValue("My response1 is empty")
                .build();

        QuestionResponse q2 = QuestionResponse.builder()
                .type(QuestionType.OPEN_TEXT)
                .questionNumber(2)
                .responseValue("My response2 is empty")
                .build();

        QuestionResponse q3 = QuestionResponse.builder()
                .type(QuestionType.METRIC_RANGE)
                .questionNumber(3)
                .responseValue(5)
                .build();

        QuestionResponse q4 = QuestionResponse.builder()
                .type(QuestionType.METRIC_RANGE)
                .questionNumber(4)
                .responseValue(2)
                .build();

        RespondentInfoResponse info1 = RespondentInfoResponse.builder()
                .key("Email")
                .value("<EMAIL>")
                .build();

        // NOTE: question & info order in list aren't important as they should be rearranged
        SurveyResponse surveyResponse = SurveyResponse.builder()
                .questionResponses(Arrays.asList(q1, q3, q4, q2))
                .respondentInfoResponses(Arrays.asList(info1))
                .build();

        // When
        surveyService.putResponsesToSurvey(uuid, surveyResponse);

        // Then
        verify(userContentBatchMapper, timeout(200)).insertList(listCaptor.capture(), eq(200));
        List<UserContent> contents = listCaptor.getValue();
        assertEquals(2, contents.size());
        assertEquals("My response1 is empty", contents.get(0).getContent());
        assertEquals(USER_ID, contents.get(0).getUserId().intValue());
        assertNotNull(contents.get(0).getTimestamp());
        assertNotNull(contents.get(0).getContentTimestamp());
        assertEquals(5, (contents.get(0).getMetadata().length));
        assertEquals(survey.getQuestions().get(0).getDescription(), contents.get(0).getMetadata()[0]);
        assertEquals(5, ((Integer) contents.get(0).getMetadata()[1]).intValue());
        assertEquals(2, ((Integer) contents.get(0).getMetadata()[2]).intValue());
        assertEquals("<EMAIL>", contents.get(0).getMetadata()[3]);
        assertEquals("", contents.get(0).getMetadata()[4]);

        assertEquals("My response2 is empty", contents.get(1).getContent());
        assertEquals(USER_ID, contents.get(1).getUserId().intValue());
        assertNotNull(contents.get(1).getTimestamp());
        assertNotNull(contents.get(1).getContentTimestamp());
        assertEquals(5, (contents.get(1).getMetadata().length));
        assertEquals(survey.getQuestions().get(1).getDescription(), contents.get(1).getMetadata()[0]);
        assertEquals(5, ((Integer) contents.get(1).getMetadata()[1]).intValue());
        assertEquals(2, ((Integer) contents.get(1).getMetadata()[2]).intValue());
        assertEquals("<EMAIL>", contents.get(1).getMetadata()[3]);
        assertEquals("", contents.get(1).getMetadata()[4]);
    }

    @Test
    public void shouldNotPutResponseToSurveySurveyNotLive() throws Exception {
        // Given
        String uuid = "123-456-789";
        Survey survey = buildDummySurvey("my survey",
                SurveyStatus.DRAFT,
                DUMMY_QUESTIONS,
                null);
        survey.setDatasetId(200);

        given(surveyMapper.selectSurveyByUuid(uuid)).willReturn(Optional.of(survey));

        QuestionResponse q1 = QuestionResponse.builder()
                .type(QuestionType.OPEN_TEXT)
                .questionNumber(1)
                .responseValue("My response1 is empty")
                .build();

        QuestionResponse q2 = QuestionResponse.builder()
                .type(QuestionType.OPEN_TEXT)
                .questionNumber(2)
                .responseValue("My response2 is empty")
                .build();

        QuestionResponse q3 = QuestionResponse.builder()
                .type(QuestionType.METRIC_RANGE)
                .questionNumber(3)
                .responseValue(5)
                .build();

        QuestionResponse q4 = QuestionResponse.builder()
                .type(QuestionType.METRIC_RANGE)
                .questionNumber(4)
                .responseValue(2)
                .build();

        RespondentInfoResponse info1 = RespondentInfoResponse.builder()
                .key("Email")
                .value("<EMAIL>")
                .build();

        RespondentInfoResponse info2 = RespondentInfoResponse.builder()
                .key("Phone")
                .value("+44 123 4567")
                .build();

        SurveyResponse surveyResponse = SurveyResponse.builder()
                .questionResponses(Arrays.asList(q1, q2, q3, q4))
                .respondentInfoResponses(Arrays.asList(info1, info2))
                .build();

        // When
        try {
            surveyService.putResponsesToSurvey(uuid, surveyResponse);
        } catch (EmoticsException e) {
            // Then
            assertEquals("Survey with UUID 123-456-789 is not ready for responses", e.getMessage());
        }
        verify(userContentBatchMapper, times(0)).insertList(anyListOf(UserContent.class), anyInt());
    }

    @Test
    public void shouldNotPutResponseToSurveyWithRequiredRespondentInfoNotFilledIn() throws Exception {
        // Given
        ArgumentCaptor<List<UserContent>> listCaptor
                = ArgumentCaptor.forClass((Class) List.class);

        String uuid = "123-456-789";
        Survey survey = buildDummySurvey("my survey",
                SurveyStatus.LIVE,
                DUMMY_QUESTIONS,
                Configurations.builder()
                        .respondentInfo(RespondentInfo.builder()
                                .displayed(true)
                                .properties(Arrays.asList(InfoProperty.builder()
                                                .required(true)
                                                .key("Email")
                                                .description("Email")
                                                .type(RespondentInfoType.EMAIL)
                                                .build(),
                                        InfoProperty.builder()
                                                .required(false)
                                                .key("Phone")
                                                .description("Phone")
                                                .type(RespondentInfoType.PHONE)
                                                .build()))
                                .build())
                        .build());
        survey.setDatasetId(200);

        given(surveyMapper.selectSurveyByUuid(uuid)).willReturn(Optional.of(survey));
        given(datasetMapper.selectById(anyInt())).willReturn(buildDummyDataset(200, Arrays.asList("Question")));

        QuestionResponse q1 = QuestionResponse.builder()
                .type(QuestionType.OPEN_TEXT)
                .questionNumber(1)
                .responseValue("My response1 is empty")
                .build();

        QuestionResponse q2 = QuestionResponse.builder()
                .type(QuestionType.OPEN_TEXT)
                .questionNumber(2)
                .responseValue("My response2 is empty")
                .build();

        QuestionResponse q3 = QuestionResponse.builder()
                .type(QuestionType.METRIC_RANGE)
                .questionNumber(3)
                .responseValue(5)
                .build();

        QuestionResponse q4 = QuestionResponse.builder()
                .type(QuestionType.METRIC_RANGE)
                .questionNumber(4)
                .responseValue(2)
                .build();

        RespondentInfoResponse info1 = RespondentInfoResponse.builder()
                .key("Phone")
                .value("+1234567")
                .build();

        // NOTE: question & info order in list aren't important as they should be rearranged
        SurveyResponse surveyResponse = SurveyResponse.builder()
                .questionResponses(Arrays.asList(q1, q3, q4, q2))
                .respondentInfoResponses(Arrays.asList(info1))
                .build();

        // When
        try {
            surveyService.putResponsesToSurvey(uuid, surveyResponse);
        } catch (EmoticsException e) {
            // Then
            assertEquals("Property 'Email' is required", e.getMessage());
        }
        verify(userContentBatchMapper, times(0)).insertList(anyListOf(UserContent.class), anyInt());
    }

    @Test
    public void shouldNotPutResponseToSurveyWithoutAllMetricQuestionsFilled() throws Exception {
        // Given
        ArgumentCaptor<List<UserContent>> listCaptor
                = ArgumentCaptor.forClass((Class) List.class);

        String uuid = "123-456-789";
        Survey survey = buildDummySurvey("my survey",
                SurveyStatus.LIVE,
                DUMMY_QUESTIONS,
                Configurations.builder()
                        .respondentInfo(RespondentInfo.builder()
                                .displayed(true)
                                .properties(Arrays.asList(InfoProperty.builder()
                                                .required(true)
                                                .key("Email")
                                                .description("Email")
                                                .type(RespondentInfoType.EMAIL)
                                                .build(),
                                        InfoProperty.builder()
                                                .required(false)
                                                .key("Phone")
                                                .description("Phone")
                                                .type(RespondentInfoType.PHONE)
                                                .build()))
                                .build())
                        .build());
        survey.setDatasetId(200);

        given(surveyMapper.selectSurveyByUuid(uuid)).willReturn(Optional.of(survey));
        given(datasetMapper.selectById(anyInt())).willReturn(buildDummyDataset(200, Arrays.asList("Question")));

        QuestionResponse q1 = QuestionResponse.builder()
                .type(QuestionType.OPEN_TEXT)
                .questionNumber(1)
                .responseValue("My response1 is empty")
                .build();

        QuestionResponse q2 = QuestionResponse.builder()
                .type(QuestionType.OPEN_TEXT)
                .questionNumber(2)
                .responseValue("My response2 is empty")
                .build();

        QuestionResponse q3 = QuestionResponse.builder()
                .type(QuestionType.METRIC_RANGE)
                .questionNumber(3)
                .responseValue(5)
                .build();

        RespondentInfoResponse info1 = RespondentInfoResponse.builder()
                .key("Email")
                .value("<EMAIL>")
                .build();

        // NOTE: question & info order in list aren't important as they should be rearranged
        SurveyResponse surveyResponse = SurveyResponse.builder()
                .questionResponses(Arrays.asList(q1, q3, q2))
                .respondentInfoResponses(Arrays.asList(info1))
                .build();

        // When
        surveyService.putResponsesToSurvey(uuid, surveyResponse);

        // Then
        verify(userContentBatchMapper, timeout(200)).insertList(listCaptor.capture(), eq(200));
        List<UserContent> contents = listCaptor.getValue();
        assertEquals(2, contents.size());
        assertEquals("My response1 is empty", contents.get(0).getContent());
        assertEquals(USER_ID, contents.get(0).getUserId().intValue());
        assertNotNull(contents.get(0).getTimestamp());
        assertNotNull(contents.get(0).getContentTimestamp());
        assertEquals(5, (contents.get(0).getMetadata().length));
        assertEquals(survey.getQuestions().get(0).getDescription(), contents.get(0).getMetadata()[0]);
        assertEquals(5, ((Integer) contents.get(0).getMetadata()[1]).intValue());
        assertEquals(null, contents.get(0).getMetadata()[2]);
        assertEquals("<EMAIL>", contents.get(0).getMetadata()[3]);
        assertEquals("", contents.get(0).getMetadata()[4]);

        assertEquals("My response2 is empty", contents.get(1).getContent());
        assertEquals(USER_ID, contents.get(1).getUserId().intValue());
        assertNotNull(contents.get(1).getTimestamp());
        assertNotNull(contents.get(1).getContentTimestamp());
        assertEquals(5, (contents.get(1).getMetadata().length));
        assertEquals(survey.getQuestions().get(1).getDescription(), contents.get(1).getMetadata()[0]);
        assertEquals(5, ((Integer) contents.get(1).getMetadata()[1]).intValue());
        assertEquals(null, contents.get(1).getMetadata()[2]);
        assertEquals("<EMAIL>", contents.get(1).getMetadata()[3]);
        assertEquals("", contents.get(1).getMetadata()[4]);
    }

    private static Survey buildDummySurvey(String label, SurveyStatus surveyStatus, List<SurveyQuestion> questions, Configurations configs) {
        return SurveyWithUser.builder()
                .id(SURVEY_ID)
                .userId(USER_ID)
                .label(label)
                .workspaceId(WORKSPACE_ID)
                .status(surveyStatus)
                .questions(questions)
                .configurations(configs)
                .build();
    }

    private static Dataset buildDummyDataset(int datasetId, List<String> metadataHeader) {
        Dataset dataset = new Dataset();

        dataset.setId(datasetId);
        dataset.setMetadataHeaders(metadataHeader);

        return dataset;
    }
}
